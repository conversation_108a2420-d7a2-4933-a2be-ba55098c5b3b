from time import time
from dataclasses import dataclass
from typing import Tuple
import os
import difflib, heapq
import torch,torchvision
# torch.backends.cuda.matmul.allow_tf32 = True
# torch.backends.cudnn.allow_tf32 = False
# torch.backends.cudnn.deterministic = True
import torch.nn as nn
import torch.nn.functional as F
import kornia
import inspect
import cv2, numpy as np
from PIL import Image
# import pyclipper

# File "/usr/local/lib/python3.9/site-packages/kornia/geometry/conversions.py", line 1101, in normalize_homography
# File "/usr/local/lib/python3.9/site-packages/kornia/utils/helpers.py", line 123, in _torch_inverse_cast
#   return torch.inverse(input.to(dtype)).to(input.dtype)
# RuntimeError: CUDA error:  when calling `cusparseCreate(handle)`
# Monkey goes here.
if torch.cuda.is_available() and (torch.cuda.get_device_name(0).startswith('Iluvatar')
    or True):
    _torch_inverse_orig = torch.inverse
    def _torch_inverse(x):
        return _torch_inverse_orig(x.cpu()).to(x)
    torch.inverse = _torch_inverse

__all__ = ['OCRSystem']


@dataclass
class OCRSystemMeta:
    det_model_path:str=None
    det_max_side_len:float=960
    det_db_thresh:float=0.3
    det_db_box_thresh:float=0.5
    det_db_unclip_ratio:float=2.0
    det_db_min_size:int=3
    det_per_img:int=1000
    rec_model_path:str=None
    rec_char_dict_path:str=None
    rec_image_shape:str='3,32,320'
    rec_char_type:str='ch'
    rec_batch_num:int=30
    rec_max_text_length:int=25
    rec_use_space_char:bool=True
    cls_model_path:str=None
    cls_vertical_model_path:str=None
    cls_image_shape:str='3,48,192'
    cls_batch_num:int=30
    cls_thresh:float=0.9
    device:str='cuda' if torch.cuda.is_available() else 'cpu'
    enable_quantization:bool=True  # 新增：是否启用量化优化


class OCRSystem(nn.Module, OCRSystemMeta):

    def __init__(self, **kwargs):
        nn.Module.__init__(self)
        OCRSystemMeta.__init__(self, **kwargs)
        self.hparams = kwargs
        self._text_detector = self.get_or_load(self.det_model_path)
        self._text_classifier = self.get_or_load(self.cls_model_path)
        self._text_vertical = self.get_or_load(self.cls_vertical_model_path)
        self._text_recognizer = self.get_or_load(self.rec_model_path)
        self._chars, self._chardict = self.chars_and_char_dict(
            self.rec_char_dict_path, self.rec_use_space_char)
        self._thresh_gap = np.log(len(self._chars))
        # from torchaudio.models.decoder import ctc_decoder
        # self._decoder = ctc_decoder('A', self._chars, beam_size_token=50, nbest=3,
        #         blank_token='\x00', sil_token=' ', word_score=-0.1)

    def get_or_load(self, path):
        model = torch.jit.load(path, map_location=self.device)

        # 在CPU上启用量化优化
        if self.device == 'cpu' and hasattr(self, 'enable_quantization') and self.enable_quantization:
            try:
                # 根据模型类型选择不同的量化策略
                if 'rec_model' in str(path) or 'tp1t7' in str(path):
                    # 识别模型使用FP16量化保持精度
                    model = torch.quantization.quantize_dynamic(
                        model,
                        {torch.nn.Linear, torch.nn.Conv2d},
                        dtype=torch.float16
                    )
                    print(f"✅ 已对识别模型 {path} 应用FP16量化优化")
                else:
                    # 其他模型(检测、分类、版面分析)使用INT8量化
                    model = torch.quantization.quantize_dynamic(
                        model,
                        {torch.nn.Linear, torch.nn.Conv2d},
                        dtype=torch.qint8
                    )
                    print(f"✅ 已对模型 {path} 应用INT8量化优化")
            except Exception as e:
                print(f"⚠️ 模型 {path} 量化失败，使用原始模型: {e}")

        return model

    @staticmethod
    def chars_and_char_dict(rec_char_dict_path, rec_use_space_char=True):
        chars, chardict = [], {}
        if rec_char_dict_path is not None:
            with open(rec_char_dict_path, 'r', encoding='utf-8') as f:
                chars.extend([l.strip('\r\n') for l in f])
            if rec_use_space_char:
                chars.append(' ')
            chars.append('\x00')
            chardict = { j:i for i,j in enumerate(chars) }
        return chars, chardict

    # @profile
    def text_detect(self, img: torch.Tensor, yielding = lambda: None):
        max_side_len = self.det_max_side_len
        h, w = img.shape[-2:]
        if h == 0 or w == 0:
            return torch.empty(0,4,2,dtype=torch.float32), torch.empty(0,6,dtype=torch.float32)
        x = img.to(torch.float32) / 255.
        if max(h, w) > max_side_len:
            if h > w:
                h, w = max_side_len, round(max_side_len / h * w)
            else:
                h, w = round(max_side_len / w * h), max_side_len
            h, w = int(h), int(w)
            x = F.interpolate(x, [h, w], mode='bilinear', align_corners=False)
        hscale, wscale = img.shape[-2] / h, img.shape[-1] / w
        rscale = np.sqrt(hscale * wscale)
        dtype, device = x.dtype, x.device
        mean = torch.as_tensor([0.485, 0.456, 0.406], dtype=dtype, device=device)
        std = torch.as_tensor([0.229, 0.224, 0.225], dtype=dtype, device=device)
        x = (x - mean[:, None, None]) / std[:, None, None]
        pad_h = 0 if h % 32 == 0 else 32 - h % 32
        pad_w = 0 if w % 32 == 0 else 32 - w % 32
        ### pad at top-left gives nice results for up-right pages.
        # x = F.pad(x, [0, pad_w, 0, pad_h])
        # maps = self._text_detector(x)[:,:,:h,:w]
        x = F.pad(x, [pad_w, 0, pad_h, 0])
        maps = self._text_detector(x)[:,:,pad_h:,pad_w:]
        # pad = [pad_w-pad_w//2, pad_w//2, pad_h-pad_h//2, pad_h//2]
        # x = F.pad(x, pad)
        # maps = self._text_detector(x)[:,:,pad[2]:pad[2]+h,pad[0]:pad[0]+w]
        maps = (maps*255).round().to(torch.uint8)[0,0]
        yielding()
        maps = maps.cpu().numpy()
        # cv2.imwrite('{}.png'.format(_dbg_i), maps)
        mask = cv2.threshold(maps, self.det_db_thresh*255, 255, cv2.THRESH_BINARY)[1]
        mask = cv2.dilate(mask, np.ones([2,2])) # np.array([[1,1],[1,1]]))
        outs = cv2.findContours(mask, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        if len(outs) == 3:
            contours = outs[1]
        elif len(outs) == 2:
            contours = outs[0]
        contours = contours[:self.det_per_img]
        boxes, rects = [], []
        sec = 0
        for i, contour in enumerate(contours):
            rect = cv2.minAreaRect(contour)
            if min(rect[1]) < self.det_db_min_size:
                continue
            points = cv2.boxPoints(rect)
            xywh = cv2.boundingRect(points)
            map_ = maps[xywh[1]:xywh[1]+xywh[3], xywh[0]:xywh[0]+xywh[2]]
            mask_ = np.zeros(map_.shape, dtype=np.uint8)
            # points_ = points.copy()
            # points_[:] -= xyxy[:2]
            # cv2.fillPoly(mask_, points_.reshape(1,-1,2).round().astype(np.int32), 1)
            try:
                cv2.fillPoly(mask_, points.reshape(1,-1,2).astype(np.int32), 1, offset=[-xywh[0], -xywh[1]])
            except:
                pass
            score = cv2.mean(map_, mask_)[0] / 255
            if score < self.det_db_box_thresh:
                continue
            distance = (rect[1][0]*rect[1][1]) * self.det_db_unclip_ratio / ((rect[1][0]+rect[1][1])*2)
            # offset = pyclipper.PyclipperOffset()
            # offset.AddPath(points, pyclipper.JT_ROUND, pyclipper.ET_CLOSEDPOLYGON)
            # contour_ = np.array(offset.Execute(distance)).reshape(-1,1,2)
            # rect_ = cv2.minAreaRect(contour_)
            rect_ = (rect[0], (rect[1][0]+distance*2, rect[1][1]+distance*2), rect[2])
            if min(rect_[1]) < self.det_db_min_size + 2:
                continue
            if rect_[1][0] > rect_[1][1]:
                rect_ = (rect_[0], (rect_[1][1],rect_[1][0]), 90+rect_[2])
            else:
                if rect_[1][1] < 1.5 * rect_[1][0]:
                    if rect_[2] < 45:
                        rect_ = (rect_[0], (rect_[1][1],rect_[1][0]), 90+rect_[2])
                else:
                    if rect_[2] == 0:
                        rect_ = (rect_[0], rect_[1], 180)
            rect_ = ((rect_[0][0]*wscale,rect_[0][1]*hscale), 
                     (rect_[1][0]*rscale,rect_[1][1]*rscale), rect_[2])
            points_ = cv2.boxPoints(rect_)
            boxes.append(points_)
            rects.append([rect_[0][0], rect_[0][1], rect_[1][0], rect_[1][1], rect_[2], score])
            # print(rect_, rect, distance, score)
            # print(points_.round().astype(np.int16).tolist())
        boxes = np.asarray(boxes, dtype=np.float32).reshape(-1,4,2)
        # boxes[:,:,0] *= wscale
        # boxes[:,:,1] *= hscale
        rects = np.asarray(rects, dtype=np.float32).reshape(-1,6)
        u = self._argsort_boxes(boxes)
        return boxes[u], rects[u]

    @staticmethod
    def _argsort_boxes(boxes):
        e = np.rec.array(boxes.min(1), dtype=[('x', np.float32), ('y', np.float32)]).reshape(-1)
        u = np.argsort(e, 0, order=['y','x'])
        for i in range(len(u)-1):
            j, k = u[i], u[i+1]
            l, m = e[j], e[k]
            if abs(m[1] - l[1]) < 10 and m[0] < l[0]:
                u[i], u[i+1] = k, j
        return u

    @staticmethod
    # @profile
    def _resample_to(img: torch.Tensor, boxes: np.ndarray, rects: np.ndarray, dsize: Tuple[int,int]):
        points_src = torch.as_tensor(boxes)
        points_des = torch.empty_like(points_src)
        rects = torch.as_tensor(rects)
        h,w = dsize
        des_ws = torch.minimum(rects[:,3:4]/rects[:,2:3]*h, torch.as_tensor(w))
        points_des[:,:2,1] = 0.  # y of p0x1 = 0
        points_des[:,2:,1] = h   # y of p2p3 = h
        points_des[:,[0,3],0] = 0. # x of p0p3 = 0
        points_des[:,[1,2],0] = des_ws
        m = kornia.geometry.get_perspective_transform(points_src, points_des)
        m_ = m.to(img.device)
        patch = kornia.geometry.warp_perspective(img.expand(m_.size()[0],-1,-1,-1), m_, [h,w],
            padding_mode='border') # TIME 0.01 B=75
        des_ws = des_ws.ceil().to(torch.int)
        return patch, des_ws

    @staticmethod
    def extract_patches(img: torch.Tensor, boxes: np.ndarray, rects: np.ndarray, dsize: list[int]):
        patch, des_ws = OCRSystem._resample_to(img, boxes, rects, dsize)
        patch = (patch / 255. - 0.5) / 0.5
        for i in range(len(des_ws)):
            des_w = des_ws[i]
            patch[i,:,:,des_w:] = 0
        return patch

    @property
    def cls_dsize(self) -> list[int]:
        return [int(i) for i in self.cls_image_shape.split(',')[1:]]

    def text_cls(self, img: torch.Tensor, boxes: np.ndarray, rects: np.ndarray):
        dsize = self.cls_dsize
        patch = self.extract_patches(img, boxes, rects, dsize)
        res = self._text_classifier(patch) # probs, cls
        # cls = res[1]
        # for i in range(len(des_ws)):
        #     des_w, ci = des_ws[i], cls[i]
        #     cv2.imwrite('P7_{:04d}_{:d}.jpg'.format(i, ci), pcopy[i,:,:,:des_w].cpu().permute(1,2,0).numpy())
        return res

    @property
    def rec_dsize(self) -> list[int]:
        return [int(i) for i in self.rec_image_shape.split(',')[1:]]

    # @profile
    def text_rec_logit(self, img: torch.Tensor, boxes: np.ndarray, rects: np.ndarray, fx: int = 1):
        dsize = self.rec_dsize; dsize[-1] *= fx
        patch = self.extract_patches(img, boxes, rects, dsize)
        res = self._text_recognizer(patch) # TxBxW
        # for i in range(len(des_ws)):
        #     des_w = des_ws[i]
        #     cv2.imwrite('T7_{:04d}.jpg'.format(i), pcopy[i,:,:,:des_w].cpu().permute(1,2,0).numpy())
        return res

    # generate classic text recognization result
    # @profile
    def text_rec_classic(self, img: torch.Tensor, boxes: np.ndarray, rects: np.ndarray, 
            fx: int = 1, yielding = lambda: None):
        rec = self.text_rec_logit(img, boxes, rects, fx)
        rec = torch.softmax(rec, -1)
        rv, rid = torch.max(rec, -1)
        yielding()
        # rec = rec.permute([1,0,2]).contiguous()
        rv = rv.cpu(); rid = rid.cpu()
        # batch_hypos = self._decoder(rec.cpu()); print(batch_hypos)
        # hypo = [[''.join(self._decoder.idxs_to_tokens(h.tokens)).strip() for h in hy] for hy in hypo]
        # transcripts = [[''.join(hypo.words).strip() for hypo in hypos] for hypos in batch_hypos]
        # print(transcripts)
        blank = rec.shape[-1] - 1
        rec_res = []
        for i in range(rid.shape[1]):
            ri  = rid[:, i]
            ib  = ri != blank
            ri  = torch.unique_consecutive(ri) #, return_counts=True)
            ri  = ri[ri != blank] 
            rvi = rv[ib, i]
            score = rvi.mean().nan_to_num()
            rec_res.append([''.join([self._chars[j] for j in ri]), score.item()])
        return rec_res

    def text_rec_1(self, img: torch.Tensor, boxes: np.ndarray, rects: np.ndarray):
        rec = self.text_rec_logit(img, boxes, rects)
        rv, rid = torch.max(rec, -1)
        rid = rid.cpu()
        rec_res = []
        for i in range(rid.shape[1]):
            ri  = rid[:, i]
            rec_res.append([''.join([self._chars[j] for j in ri]), 0.0])
        return rec_res

    @torch.no_grad()
    def text_catid(self, img: torch.Tensor, boxes: np.ndarray, rects: np.ndarray, cds: list[str], fx: int = 1):
        rec = self.text_rec_logit(img, boxes, rects, fx)
        rid = torch.max(rec, -1)[1]
        blank = rec.shape[-1] - 1
        all_results = []
        labels_list = []
        for bidx, a in enumerate(cds):
            labels_list.append(np.array([self._chardict[c] for c in a], dtype=np.int64))
        rid = rid.cpu()
        for i in range(rid.shape[1]):
            ri  = torch.unique_consecutive(rid[:, i])
            ri  = ri[ri != blank]
            matched = get_close_matches(ri.numpy(), labels_list)
            if not matched:
                continue
            if matched == 1:
                b = ''.join([self._chars[j] for j in ri])
                all_results.append((b, 1.0, None, None, None))
                continue
            matched = [torch.as_tensor(j) for j in matched]
            matched.append(ri)
            log_probs = rec[:,i:i+1,:].expand(-1,len(matched),-1)
            targets = torch.cat(matched).to(log_probs)
            input_lengths = torch.full((len(matched),), log_probs.shape[0], dtype=torch.long)
            target_lengths = torch.as_tensor([len(j) for j in matched], dtype=torch.long)
            losses = F.ctc_loss(log_probs, targets, input_lengths, target_lengths, blank, 
                    reduction='none').cpu()
            mlos, midx = losses[:-1].min(-1)
            glos = losses[-1]
            b = ''.join([self._chars[j] for j in ri])
            a = ''.join([self._chars[j] for j in matched[midx]])
            r = difflib.SequenceMatcher(a=a,b=b).ratio()
            if mlos - glos <= self._thresh_gap / r:
                all_results.append((a, r, b, mlos.item(), glos.item()))
        return all_results


    def ocr_ie(self, images):
        """
        信息抽取定制的OCR功能
            每个张图片的结果形如：[[box1, (text1, score1, char_coords1)], [box2, (text2, score2, char_coords2)], ... ]。最终结果是所有图片结果组合成的list。
        [box_i, (text_i, score_i, char_coords_i)]代表该图片的第i个文本框的结果。
        其中box记录了四个顶点的坐标，text是该文本框识别出的文字，score是识别出该文字的分数，char_coords是每个字的质心的坐标。
        例如：
            box: [[886, 176], [1638, 176], [1638, 253], [886, 253]]
            text: '不动产登记申请审批表'
            score: 0.9997788667678833
            char_coords: [[919, 214], [996, 214], [1063, 214], [1139, 214], [1216, 214], [1292, 214], [1369, 214], [1446, 214], [1513, 214], [1589, 214]]
        :param image: image path
        :return:
        """
        if isinstance(images, str) or isinstance(images, Image.Image) or isinstance(images, np.ndarray):
            images = [images]
        res = []
        for image in images:
            with torch.inference_mode():
                # img = cv2.imread(image, cv2.IMREAD_COLOR)
                # img = cv2.imdecode(np.fromfile(image, dtype=np.uint8), cv2.IMREAD_COLOR)
                if isinstance(image, str):
                    img = np.asarray(Image.open(image).convert("RGB"))[:, :, [2, 1, 0]]  # 转化为BGR
                elif isinstance(image, Image.Image):
                    img = np.asarray(image.convert("RGB"))[:, :, [2, 1, 0]]  # 转化为BGR
                elif isinstance(image, np.ndarray):
                    img = image
                else:
                    raise ValueError()
                # rot = cv2.getRotationMatrix2D([img.shape[1]/2,img.shape[0]/2], p*1.0, 1.0)
                # img = cv2.warpAffine(img, rot, [img.shape[1],img.shape[0]])
                img_ = img

                img_ = torch.as_tensor(img_, device=self.device)[None, :].permute([0, 3, 1, 2]).to(torch.float32)

                boxes, rects = self.text_detect(img_)
                if len(boxes) < 1:
                    res.append([])
                    continue
                # cv2.polylines(img, boxes.round().astype(np.int32), 1, (255, 0, 0))
                # cv2.imwrite(wname, img, [cv2.IMWRITE_JPEG_QUALITY, 60])
                ## cls = ocr.text_cls(img_, boxes, rects)
                # rec_res = ocr.text_rec_classic(img_, boxes, rects)
                rec_res = self.text_rec_1(img_, boxes, rects)
                res.append(rec_res)
        return res


# return 1 for perfect match or a list of close matches.
def get_close_matches(word, possibilities, n=3, cutoff=0.6):
    if not n >  0:
        raise ValueError("n must be > 0: %r" % (n,))
    if not 0.0 <= cutoff <= 1.0:
        raise ValueError("cutoff must be in [0.0, 1.0]: %r" % (cutoff,))
    result = []
    s = difflib.SequenceMatcher()
    s.set_seq2(word)
    for x in possibilities:
        s.set_seq1(x)
        if s.real_quick_ratio() >= cutoff and \
           s.quick_ratio() >= cutoff and \
           s.ratio() >= cutoff:
            if s.ratio() == 1.0:
                return 1
            result.append((s.ratio(), x))
    result = heapq.nlargest(n, result, key=lambda x: x[0])
    return [x for _, x in result]


if __name__ == '__main__':
    cv2.setNumThreads(0)
    torch.set_num_threads(1)
    torch._C._jit_set_profiling_mode(False)
    from importlib_resources import files
    dtm = files('dtm')

    ocr = OCRSystem(det_model_path=dtm/'001-1.pt', rec_image_shape='3,32,320',
        cls_model_path=dtm/'003.pt', cls_vertical_model_path=dtm/'006.pt',
        rec_model_path=dtm/'tp1t7.pt',
        # rec_model_path=dtm/'002R.pt',
        rec_char_dict_path=dtm/'ppocr_keys_v1.txt') #, det_max_side_len=1152)
        # rec_char_dict_path=dtm/'002R_keys_v1') #, det_max_side_len=1152)
    fname = '0010-001-00001-0001.jpg'
    # imc = 7
    imc = 8
    # for i in range(1, imc+1):
    #     global _dbg_i
    #     _dbg_i = f'0815np/{i}'
    #     fname = f'tp1t7/{i}.png'
    #     fname = f'0815np/{i}.jpg'
    #     print(fname)

    s = 0
    with torch.inference_mode(): #, torch.autocast('cuda'):
      for p in [i*10 for i in range(36)]:
        wname = ('r{:03d}_'.format(p)+fname)
        # wname = ('{}.msk.jpg'.format(fname))
        print(wname)
        img = cv2.imread(fname, cv2.IMREAD_COLOR)
        # rot = cv2.getRotationMatrix2D([img.shape[1]/2,img.shape[0]/2], p*1.0, 1.0)
        # img = cv2.warpAffine(img, rot, [img.shape[1],img.shape[0]])
        img_ = img
        t = time()
        img_ = torch.as_tensor(img_, device=ocr.device)[None,:].permute([0,3,1,2]).to(torch.float32); del img
        boxes, rects = ocr.text_detect(img_)
        if len(boxes) < 1:
            continue
        # cv2.polylines(img, boxes.round().astype(np.int32), 1, (255,0,0))
        # cv2.imwrite(wname, img, [cv2.IMWRITE_JPEG_QUALITY, 60])
        # cls = ocr.text_cls(img_, boxes, rects)
        rec_res = ocr.text_rec_classic(img_, boxes, rects); del img_
        # rec_res = ocr.text_catid(img_, boxes, rects, ['不动产登记审批表','不动产登记审批书']); del img_
        # rec_res = ocr.text_rec_1(img_, boxes, rects)
        # i = 6
        # print(rec_res[i], boxes[i], rects[i])
        # if rects[i][3] / rects[i][2] < 10:
        #     each = rects[i][2] * 10 / 80
        # else:
        #     each = rects[i][3] / 80
        # print('each', each)
        # txt = rec_res[i][0]
        # for j in range(80):
        #     if txt[j] != '\0':
        #         print(txt[j], j*each)
        # print('length', rects[i][3])
        # print(rec_res)
        # exit(0)
        s += (p != 0) * (time() - t)
    from sys import stderr
    print(s, file=stderr)
    print(torch.cuda.mem_get_info())
    print(torch.cuda.empty_cache())
    print(torch.cuda.mem_get_info())
