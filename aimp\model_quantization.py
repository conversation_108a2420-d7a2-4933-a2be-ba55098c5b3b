#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用模型量化管理器

为系统中所有深度学习模型提供统一的量化优化功能，包括：
- OCR模型 (检测、识别、分类)
- 版面分析模型
- 污点检测模型  
- 关键点检测模型
- 图像分类模型
- 对象检测模型

作者: xubiyun
日期: 2025-06-16
"""

import torch
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class ModelQuantizationManager:
    """模型量化管理器"""
    
    # 量化策略配置
    QUANTIZATION_STRATEGIES = {
        # OCR相关模型
        'det_model': {
            'dtype': torch.qint8,
            'description': '文本检测模型 - INT8量化',
            'layers': {torch.nn.Linear, torch.nn.Conv2d}
        },
        'cls_model': {
            'dtype': torch.qint8, 
            'description': '文本分类模型 - INT8量化',
            'layers': {torch.nn.Linear, torch.nn.Conv2d}
        },
        'cls_vertical_model': {
            'dtype': torch.qint8,
            'description': '垂直文本分类模型 - INT8量化', 
            'layers': {torch.nn.Linear, torch.nn.Conv2d}
        },
        'rec_model': {
            'dtype': torch.float16,
            'description': '文本识别模型 - FP16量化(保持精度)',
            'layers': {torch.nn.Linear, torch.nn.Conv2d}
        },
        
        # 版面分析和检测模型
        'layout_model': {
            'dtype': torch.qint8,
            'description': '版面分析模型 - INT8量化',
            'layers': {torch.nn.Linear, torch.nn.Conv2d}
        },
        'stain_model': {
            'dtype': torch.qint8,
            'description': '污点检测模型 - INT8量化',
            'layers': {torch.nn.Linear, torch.nn.Conv2d}
        },
        'det0789_model': {
            'dtype': torch.qint8,
            'description': '对象检测模型 - INT8量化',
            'layers': {torch.nn.Linear, torch.nn.Conv2d}
        },
        
        # 关键点和几何处理模型
        'kp_model': {
            'dtype': torch.qint8,
            'description': '关键点检测模型 - INT8量化',
            'layers': {torch.nn.Linear, torch.nn.Conv2d}
        },
        'pe_model': {
            'dtype': torch.qint8,
            'description': '透视变换模型 - INT8量化',
            'layers': {torch.nn.Linear, torch.nn.Conv2d}
        },
        
        # 图像分类模型
        'v69_model': {
            'dtype': torch.qint8,
            'description': '证件分类模型 - INT8量化',
            'layers': {torch.nn.Linear, torch.nn.Conv2d}
        }
    }
    
    def __init__(self, enable_quantization: bool = True, device: str = 'cpu'):
        """
        初始化量化管理器
        
        Args:
            enable_quantization: 是否启用量化
            device: 设备类型，量化主要用于CPU
        """
        self.enable_quantization = enable_quantization and (device == 'cpu')
        self.device = device
        self.quantized_models_cache = {}
        
        if self.enable_quantization:
            logger.info("🔧 模型量化管理器已启用 (设备: %s)", device)
        else:
            logger.info("📱 模型量化管理器已禁用 (设备: %s)", device)
    
    def get_model_type(self, model_path: str) -> Optional[str]:
        """
        根据模型路径推断模型类型
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            模型类型字符串，如果无法识别则返回None
        """
        path_str = str(model_path).lower()
        
        # OCR模型识别
        if 'tp1t7' in path_str or 'rec_model' in path_str:
            return 'rec_model'
        elif '001' in path_str or 'det_model' in path_str:
            return 'det_model'
        elif '003' in path_str or 'cls_model' in path_str:
            return 'cls_model'
        elif '006' in path_str or 'cls_vertical' in path_str:
            return 'cls_vertical_model'
        
        # 其他模型识别
        elif 'la_dp09' in path_str or 'layout_model' in path_str:
            return 'layout_model'
        elif 'dt36' in path_str or 'stain_model' in path_str:
            return 'stain_model'
        elif 'det0789' in path_str:
            return 'det0789_model'
        elif 'kp-' in path_str or 'kp_model' in path_str:
            return 'kp_model'
        elif 'mlim2a' in path_str or 'pe_model' in path_str:
            return 'pe_model'
        elif '192-v69' in path_str or 'v69_model' in path_str:
            return 'v69_model'
        
        return None
    
    def quantize_model(self, model: torch.nn.Module, model_path: str) -> torch.nn.Module:
        """
        对模型进行量化
        
        Args:
            model: 原始模型
            model_path: 模型路径（用于确定量化策略）
            
        Returns:
            量化后的模型
        """
        if not self.enable_quantization:
            return model
        
        model_type = self.get_model_type(model_path)
        if not model_type:
            logger.warning("⚠️ 无法识别模型类型: %s，跳过量化", model_path)
            return model
        
        strategy = self.QUANTIZATION_STRATEGIES.get(model_type)
        if not strategy:
            logger.warning("⚠️ 模型类型 %s 没有量化策略，跳过量化", model_type)
            return model
        
        try:
            # 执行动态量化
            quantized_model = torch.quantization.quantize_dynamic(
                model,
                strategy['layers'],
                dtype=strategy['dtype']
            )
            
            logger.info("✅ %s 量化成功: %s", strategy['description'], model_path)
            return quantized_model
            
        except Exception as e:
            logger.error("❌ 模型量化失败 %s: %s", model_path, e)
            return model
    
    def load_and_quantize(self, model_path: str, map_location: str = None) -> torch.nn.Module:
        """
        加载并量化模型
        
        Args:
            model_path: 模型文件路径
            map_location: 设备映射位置
            
        Returns:
            加载并可能量化的模型
        """
        # 使用缓存避免重复量化
        cache_key = f"{model_path}_{self.enable_quantization}_{map_location}"
        if cache_key in self.quantized_models_cache:
            return self.quantized_models_cache[cache_key]
        
        # 加载原始模型
        if map_location is None:
            map_location = self.device
            
        model = torch.jit.load(model_path, map_location=map_location)
        
        # 量化模型
        quantized_model = self.quantize_model(model, model_path)
        
        # 缓存结果
        self.quantized_models_cache[cache_key] = quantized_model
        
        return quantized_model
    
    def get_quantization_info(self) -> Dict[str, Any]:
        """
        获取量化配置信息
        
        Returns:
            量化配置的详细信息
        """
        return {
            'enabled': self.enable_quantization,
            'device': self.device,
            'strategies': {
                model_type: {
                    'dtype': str(config['dtype']),
                    'description': config['description'],
                    'layers': [layer.__name__ for layer in config['layers']]
                }
                for model_type, config in self.QUANTIZATION_STRATEGIES.items()
            },
            'cached_models': len(self.quantized_models_cache)
        }
    
    def clear_cache(self):
        """清空模型缓存"""
        self.quantized_models_cache.clear()
        logger.info("🗑️ 模型缓存已清空")


# 全局量化管理器实例
_quantization_manager = None


def get_quantization_manager(enable_quantization: bool = True, device: str = 'cpu') -> ModelQuantizationManager:
    """
    获取全局量化管理器实例
    
    Args:
        enable_quantization: 是否启用量化
        device: 设备类型
        
    Returns:
        量化管理器实例
    """
    global _quantization_manager
    
    if _quantization_manager is None:
        _quantization_manager = ModelQuantizationManager(enable_quantization, device)
    
    return _quantization_manager


def quantized_torch_jit_load(model_path: str, map_location: str = None, 
                           enable_quantization: bool = True) -> torch.nn.Module:
    """
    替代torch.jit.load的量化版本
    
    Args:
        model_path: 模型文件路径
        map_location: 设备映射位置
        enable_quantization: 是否启用量化
        
    Returns:
        加载并可能量化的模型
    """
    device = map_location or 'cpu'
    manager = get_quantization_manager(enable_quantization, device)
    return manager.load_and_quantize(model_path, map_location)
