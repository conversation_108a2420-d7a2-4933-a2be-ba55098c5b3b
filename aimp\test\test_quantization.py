#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR模型量化测试脚本

测试不同量化策略对OCR模型性能的影响：
1. 原始模型性能基准测试
2. INT8量化模型测试（检测、分类、版面分析）
3. FP16量化模型测试（文本识别）
4. 性能对比和精度评估

作者: xubiyun
日期: 2025-06-16
"""

import os
import sys
import time
import torch
import numpy as np
import cv2
from pathlib import Path
from PIL import Image
import json
from typing import Dict, List, Tuple
import argparse

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from myocr import OCRSystem


class QuantizationTester:
    """OCR模型量化测试器"""
    
    def __init__(self, test_images_dir: str = None):
        self.test_images_dir = test_images_dir or self._get_default_test_dir()
        self.results = {}
        
        # 模型路径配置
        self.model_paths = {
            'det_model_path': 'dtm/001-1.pt',
            'cls_model_path': 'dtm/003.pt', 
            'cls_vertical_model_path': 'dtm/006.pt',
            'rec_model_path': 'dtm/tp1t7.pt',
            'rec_char_dict_path': 'dtm/ppocr_keys_v1.txt'
        }
        
    def _get_default_test_dir(self) -> str:
        """获取默认测试图片目录"""
        # 可以放一些测试图片在这里
        test_dir = Path(__file__).parent / "test_images"
        test_dir.mkdir(exist_ok=True)
        return str(test_dir)
    
    def create_test_ocr_systems(self) -> Dict[str, OCRSystem]:
        """创建不同配置的OCR系统"""
        systems = {}
        
        print("🔧 创建OCR系统...")
        
        # 1. 原始模型（无量化）
        print("  - 创建原始模型...")
        systems['original'] = OCRSystem(
            device='cpu',
            enable_quantization=False,
            **self.model_paths
        )
        
        # 2. 量化模型
        print("  - 创建量化模型...")
        systems['quantized'] = OCRSystem(
            device='cpu', 
            enable_quantization=True,
            **self.model_paths
        )
        
        return systems
    
    def generate_test_image(self) -> np.ndarray:
        """生成测试图片（如果没有真实图片）"""
        # 创建一个简单的测试图片，包含一些文字
        img = np.ones((600, 800, 3), dtype=np.uint8) * 255
        
        # 添加一些文字（模拟真实场景）
        cv2.putText(img, "Test OCR Performance", (50, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(img, "Quantization Benchmark", (50, 200), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(img, "CPU Optimization Test", (50, 300), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(img, "2025-06-16", (50, 400), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        return img
    
    def get_test_images(self) -> List[np.ndarray]:
        """获取测试图片列表"""
        images = []
        
        # 尝试从测试目录加载图片
        test_dir = Path(self.test_images_dir)
        image_files = list(test_dir.glob("*.jpg")) + list(test_dir.glob("*.png"))
        
        if image_files:
            print(f"📁 从 {test_dir} 加载 {len(image_files)} 张测试图片")
            for img_path in image_files[:5]:  # 最多测试5张图片
                img = cv2.imread(str(img_path))
                if img is not None:
                    images.append(img)
        
        # 如果没有测试图片，生成一张
        if not images:
            print("📝 生成测试图片...")
            images.append(self.generate_test_image())
            
        return images
    
    def benchmark_ocr_system(self, ocr_system: OCRSystem, system_name: str, 
                           test_images: List[np.ndarray], num_runs: int = 3) -> Dict:
        """对OCR系统进行基准测试"""
        print(f"\n🧪 测试 {system_name} 系统...")
        
        results = {
            'system_name': system_name,
            'total_time': 0,
            'avg_time_per_image': 0,
            'detection_results': [],
            'recognition_results': [],
            'num_images': len(test_images),
            'num_runs': num_runs
        }
        
        total_time = 0
        all_detection_results = []
        all_recognition_results = []
        
        for run in range(num_runs):
            print(f"  运行 {run + 1}/{num_runs}...")
            run_start_time = time.time()
            
            for i, img in enumerate(test_images):
                # 转换为tensor
                img_tensor = torch.as_tensor(img, device=ocr_system.device)[None,:].permute([0,3,1,2]).to(torch.float32)
                
                # 文本检测
                with torch.inference_mode():
                    det_start = time.time()
                    boxes, rects = ocr_system.text_detect(img_tensor)
                    det_time = time.time() - det_start
                    
                    # 文本识别
                    if len(boxes) > 0:
                        rec_start = time.time()
                        rec_results = ocr_system.text_rec_classic(img_tensor, boxes, rects)
                        rec_time = time.time() - rec_start
                    else:
                        rec_results = []
                        rec_time = 0
                
                if run == 0:  # 只在第一次运行时记录结果
                    all_detection_results.append({
                        'image_idx': i,
                        'num_boxes': len(boxes),
                        'detection_time': det_time,
                        'recognition_time': rec_time,
                        'total_time': det_time + rec_time
                    })
                    all_recognition_results.extend(rec_results)
            
            run_time = time.time() - run_start_time
            total_time += run_time
            print(f"    运行时间: {run_time:.3f}s")
        
        results['total_time'] = total_time / num_runs
        results['avg_time_per_image'] = results['total_time'] / len(test_images)
        results['detection_results'] = all_detection_results
        results['recognition_results'] = all_recognition_results
        
        print(f"  ✅ 平均处理时间: {results['avg_time_per_image']:.3f}s/图")
        print(f"  📊 检测到文本框: {sum(r['num_boxes'] for r in all_detection_results)}")
        print(f"  📝 识别文本数量: {len(all_recognition_results)}")
        
        return results
    
    def compare_results(self, original_results: Dict, quantized_results: Dict) -> Dict:
        """比较原始模型和量化模型的结果"""
        print("\n📊 性能对比分析...")
        
        comparison = {
            'speed_improvement': quantized_results['avg_time_per_image'] / original_results['avg_time_per_image'],
            'original_time': original_results['avg_time_per_image'],
            'quantized_time': quantized_results['avg_time_per_image'],
            'time_saved': original_results['avg_time_per_image'] - quantized_results['avg_time_per_image'],
            'accuracy_comparison': self._compare_accuracy(original_results, quantized_results)
        }
        
        speed_up = 1 / comparison['speed_improvement']
        print(f"  🚀 速度提升: {speed_up:.2f}x")
        print(f"  ⏱️  原始模型: {comparison['original_time']:.3f}s/图")
        print(f"  ⚡ 量化模型: {comparison['quantized_time']:.3f}s/图") 
        print(f"  💾 节省时间: {comparison['time_saved']:.3f}s/图")
        
        return comparison
    
    def _compare_accuracy(self, original_results: Dict, quantized_results: Dict) -> Dict:
        """比较识别精度"""
        orig_texts = [r[0] for r in original_results['recognition_results']]
        quant_texts = [r[0] for r in quantized_results['recognition_results']]
        
        # 简单的文本匹配比较
        matches = 0
        total = min(len(orig_texts), len(quant_texts))
        
        for i in range(total):
            if orig_texts[i] == quant_texts[i]:
                matches += 1
        
        accuracy = matches / total if total > 0 else 1.0
        
        return {
            'text_match_rate': accuracy,
            'original_text_count': len(orig_texts),
            'quantized_text_count': len(quant_texts),
            'matched_texts': matches
        }
    
    def save_results(self, results: Dict, output_file: str = None):
        """保存测试结果"""
        if output_file is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            output_file = f"quantization_test_results_{timestamp}.json"
        
        output_path = Path(__file__).parent / output_file
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 测试结果已保存到: {output_path}")
    
    def run_full_test(self, num_runs: int = 3):
        """运行完整的量化测试"""
        print("🎯 开始OCR模型量化性能测试")
        print("=" * 50)
        
        # 创建OCR系统
        systems = self.create_test_ocr_systems()
        
        # 获取测试图片
        test_images = self.get_test_images()
        
        # 测试各个系统
        results = {}
        for system_name, ocr_system in systems.items():
            results[system_name] = self.benchmark_ocr_system(
                ocr_system, system_name, test_images, num_runs
            )
        
        # 性能对比
        if 'original' in results and 'quantized' in results:
            comparison = self.compare_results(results['original'], results['quantized'])
            results['comparison'] = comparison
        
        # 保存结果
        self.save_results(results)
        
        print("\n🎉 测试完成!")
        return results


def main():
    parser = argparse.ArgumentParser(description='OCR模型量化测试')
    parser.add_argument('--test-images', type=str, help='测试图片目录')
    parser.add_argument('--runs', type=int, default=3, help='测试运行次数')
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = QuantizationTester(args.test_images)
    
    # 运行测试
    results = tester.run_full_test(args.runs)
    
    return results


if __name__ == "__main__":
    main()
