# C盘空间清理工具
# 作者: Augment Agent
# 功能: 安全检测和清理C盘空间，释放磁盘空间

param(
    [switch]$CheckOnly,  # 仅检测不清理
    [switch]$AutoClean   # 自动清理（跳过确认）
)

# 颜色输出函数
function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Color
}

# 获取文件夹大小函数
function Get-FolderSize {
    param([string]$Path)
    try {
        $size = (Get-ChildItem $Path -Recurse -File -ErrorAction SilentlyContinue | 
                Measure-Object -Property Length -Sum).Sum
        if ($size -eq $null) { return 0 }
        return $size
    }
    catch {
        return 0
    }
}

# 格式化大小显示
function Format-Size {
    param([long]$Size)
    if ($Size -gt 1GB) {
        return "{0:N2} GB" -f ($Size / 1GB)
    }
    elseif ($Size -gt 1MB) {
        return "{0:N2} MB" -f ($Size / 1MB)
    }
    else {
        return "{0:N2} KB" -f ($Size / 1KB)
    }
}

# 检查磁盘空间
function Get-DiskSpace {
    Write-ColorText "=== C盘空间检测 ===" "Cyan"
    $disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "C:"}
    $totalSize = $disk.Size
    $freeSpace = $disk.FreeSpace
    $usedSpace = $totalSize - $freeSpace
    $percentFree = ($freeSpace / $totalSize) * 100
    
    Write-ColorText "总容量: $(Format-Size $totalSize)" "White"
    Write-ColorText "已使用: $(Format-Size $usedSpace)" "Yellow"
    Write-ColorText "可用空间: $(Format-Size $freeSpace)" "Green"
    Write-ColorText "可用百分比: {0:N2}%" -f $percentFree "Green"
    
    if ($percentFree -lt 10) {
        Write-ColorText "警告: C盘空间不足10%，建议立即清理！" "Red"
    }
    elseif ($percentFree -lt 20) {
        Write-ColorText "注意: C盘空间不足20%，建议清理" "Yellow"
    }
    
    return @{
        TotalSize = $totalSize
        FreeSpace = $freeSpace
        UsedSpace = $usedSpace
        PercentFree = $percentFree
    }
}

# 检查大文件夹占用
function Get-LargeFolders {
    Write-ColorText "`n=== 检查主要文件夹占用 ===" "Cyan"
    
    $folders = @("ProgramData", "Users", "Windows", "Program Files", "Program Files (x86)")
    $results = @()
    
    foreach ($folder in $folders) {
        $path = "C:\$folder"
        if (Test-Path $path) {
            Write-ColorText "正在检查 $folder..." "Gray"
            $size = Get-FolderSize $path
            $results += [PSCustomObject]@{
                Name = $folder
                Path = $path
                Size = $size
                SizeFormatted = Format-Size $size
            }
        }
    }
    
    $results | Sort-Object Size -Descending | Format-Table Name, SizeFormatted -AutoSize
    return $results
}

# 检查ProgramData详细占用
function Get-ProgramDataDetails {
    Write-ColorText "`n=== ProgramData详细占用分析 ===" "Cyan"
    
    $programDataPath = "C:\ProgramData"
    $folders = Get-ChildItem $programDataPath -Directory -ErrorAction SilentlyContinue
    $results = @()
    
    foreach ($folder in $folders) {
        Write-ColorText "检查 $($folder.Name)..." "Gray"
        $size = Get-FolderSize $folder.FullName
        if ($size -gt 100MB) {
            $results += [PSCustomObject]@{
                Name = $folder.Name
                Size = $size
                SizeFormatted = Format-Size $size
            }
        }
    }
    
    $results | Sort-Object Size -Descending | Format-Table Name, SizeFormatted -AutoSize
    return $results
}

# 清理临时文件
function Clear-TempFiles {
    Write-ColorText "`n=== 清理临时文件 ===" "Cyan"
    
    $tempPaths = @(
        $env:TEMP,
        "C:\Windows\Temp",
        "C:\ProgramData\temp"
    )
    
    $totalCleaned = 0
    
    foreach ($tempPath in $tempPaths) {
        if (Test-Path $tempPath) {
            Write-ColorText "清理 $tempPath..." "Yellow"
            try {
                $beforeSize = Get-FolderSize $tempPath
                Get-ChildItem $tempPath -Recurse -ErrorAction SilentlyContinue | 
                    Remove-Item -Force -Recurse -ErrorAction SilentlyContinue
                $afterSize = Get-FolderSize $tempPath
                $cleaned = $beforeSize - $afterSize
                $totalCleaned += $cleaned
                Write-ColorText "释放空间: $(Format-Size $cleaned)" "Green"
            }
            catch {
                Write-ColorText "清理 $tempPath 时出现错误" "Red"
            }
        }
    }
    
    Write-ColorText "临时文件总计释放: $(Format-Size $totalCleaned)" "Green"
    return $totalCleaned
}

# 清理Package Cache
function Clear-PackageCache {
    Write-ColorText "`n=== 清理Package Cache ===" "Cyan"
    
    $packageCachePath = "C:\ProgramData\Package Cache"
    if (Test-Path $packageCachePath) {
        try {
            $beforeSize = Get-FolderSize $packageCachePath
            Write-ColorText "清理前大小: $(Format-Size $beforeSize)" "Yellow"
            
            Remove-Item "$packageCachePath\*" -Recurse -Force -ErrorAction SilentlyContinue
            
            $afterSize = Get-FolderSize $packageCachePath
            $cleaned = $beforeSize - $afterSize
            Write-ColorText "释放空间: $(Format-Size $cleaned)" "Green"
            return $cleaned
        }
        catch {
            Write-ColorText "清理Package Cache时出现错误" "Red"
            return 0
        }
    }
    else {
        Write-ColorText "Package Cache目录不存在" "Gray"
        return 0
    }
}

# 清理conda缓存
function Clear-CondaCache {
    Write-ColorText "`n=== 清理Conda缓存 ===" "Cyan"
    
    try {
        $condaVersion = conda --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "发现Conda环境，开始清理缓存..." "Yellow"
            conda clean --all --yes 2>$null
            Write-ColorText "Conda缓存清理完成" "Green"
        }
        else {
            Write-ColorText "未发现Conda环境，跳过" "Gray"
        }
    }
    catch {
        Write-ColorText "清理Conda缓存时出现错误" "Red"
    }
}

# 清理Windows更新缓存
function Clear-WindowsUpdateCache {
    Write-ColorText "`n=== 清理Windows更新缓存 ===" "Cyan"
    
    $updateCachePath = "C:\Windows\SoftwareDistribution\Download"
    if (Test-Path $updateCachePath) {
        try {
            $beforeSize = Get-FolderSize $updateCachePath
            Write-ColorText "清理前大小: $(Format-Size $beforeSize)" "Yellow"
            
            Stop-Service wuauserv -Force -ErrorAction SilentlyContinue
            Remove-Item "$updateCachePath\*" -Recurse -Force -ErrorAction SilentlyContinue
            Start-Service wuauserv -ErrorAction SilentlyContinue
            
            $afterSize = Get-FolderSize $updateCachePath
            $cleaned = $beforeSize - $afterSize
            Write-ColorText "释放空间: $(Format-Size $cleaned)" "Green"
            return $cleaned
        }
        catch {
            Write-ColorText "清理Windows更新缓存时出现错误" "Red"
            return 0
        }
    }
    else {
        Write-ColorText "Windows更新缓存目录不存在" "Gray"
        return 0
    }
}

# 清理系统日志
function Clear-SystemLogs {
    Write-ColorText "`n=== 清理系统日志 ===" "Cyan"

    try {
        $logCount = 0
        wevtutil el | ForEach-Object {
            wevtutil cl $_ 2>$null
            if ($LASTEXITCODE -eq 0) { $logCount++ }
        }
        Write-ColorText "已清理 $logCount 个日志文件" "Green"
    }
    catch {
        Write-ColorText "清理系统日志时出现错误" "Red"
    }
}

# 执行所有清理操作
function Start-CleanupProcess {
    param([bool]$AutoConfirm = $false)

    Write-ColorText "`n=== 开始清理过程 ===" "Cyan"

    if (-not $AutoConfirm) {
        $confirm = Read-Host "确定要开始清理吗？这将删除临时文件、缓存等。(y/N)"
        if ($confirm -ne 'y' -and $confirm -ne 'Y') {
            Write-ColorText "清理已取消" "Yellow"
            return
        }
    }

    $totalCleaned = 0

    # 记录清理前的空间
    $beforeCleanup = Get-DiskSpace

    # 执行各项清理
    $totalCleaned += Clear-TempFiles
    $totalCleaned += Clear-PackageCache
    Clear-CondaCache
    $totalCleaned += Clear-WindowsUpdateCache
    Clear-SystemLogs

    # 记录清理后的空间
    Write-ColorText "`n=== 清理完成，检查结果 ===" "Cyan"
    $afterCleanup = Get-DiskSpace

    $actualFreed = $afterCleanup.FreeSpace - $beforeCleanup.FreeSpace

    Write-ColorText "`n=== 清理总结 ===" "Green"
    Write-ColorText "预计释放空间: $(Format-Size $totalCleaned)" "White"
    Write-ColorText "实际释放空间: $(Format-Size $actualFreed)" "Green"
    Write-ColorText "清理前可用空间: $(Format-Size $beforeCleanup.FreeSpace)" "White"
    Write-ColorText "清理后可用空间: $(Format-Size $afterCleanup.FreeSpace)" "Green"
}

# 主程序入口
function Main {
    Clear-Host
    Write-ColorText "C盘空间清理工具 v1.0" "Cyan"
    Write-ColorText "作者: Augment Agent" "Gray"
    Write-ColorText "=" * 50 "Cyan"

    # 检查是否以管理员权限运行
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
    if (-not $isAdmin) {
        Write-ColorText "建议以管理员权限运行以获得最佳清理效果" "Yellow"
    }

    # 执行检测
    $diskInfo = Get-DiskSpace
    $largeFolders = Get-LargeFolders
    $programDataDetails = Get-ProgramDataDetails

    # 根据参数决定后续操作
    if ($CheckOnly) {
        Write-ColorText "`n检测完成。使用 -AutoClean 参数可自动清理。" "Green"
        return
    }

    if ($AutoClean) {
        Start-CleanupProcess -AutoConfirm $true
    }
    else {
        Write-ColorText "`n是否要开始清理过程？" "Yellow"
        Write-ColorText "1. 开始清理" "White"
        Write-ColorText "2. 仅查看检测结果" "White"
        Write-ColorText "3. 退出" "White"

        $choice = Read-Host "请选择 (1-3)"

        switch ($choice) {
            "1" { Start-CleanupProcess }
            "2" { Write-ColorText "检测结果已显示在上方" "Green" }
            "3" { Write-ColorText "再见！" "Green" }
            default { Write-ColorText "无效选择，退出程序" "Red" }
        }
    }
}

# 运行主程序
try {
    Main
}
catch {
    Write-ColorText "程序执行出现错误" "Red"
    Write-ColorText "请以管理员权限重新运行此脚本" "Yellow"
}

Write-ColorText "`n按任意键退出..." "Gray"
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
