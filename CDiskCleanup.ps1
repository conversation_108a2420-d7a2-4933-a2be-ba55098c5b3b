# C Disk Space Cleanup Tool
# Author: Augment Agent
# Function: Safely detect and clean C disk space

param(
    [switch]$CheckOnly,  # Check only, no cleanup
    [switch]$AutoClean   # Auto cleanup without confirmation
)

# Color output function
function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Color
}

# Get folder size function
function Get-FolderSize {
    param([string]$Path)
    try {
        $size = (Get-ChildItem $Path -Recurse -File -ErrorAction SilentlyContinue | 
                Measure-Object -Property Length -Sum).Sum
        if ($size -eq $null) { return 0 }
        return $size
    }
    catch {
        return 0
    }
}

# Format size display
function Format-Size {
    param([long]$Size)
    if ($Size -gt 1GB) {
        return "{0:N2} GB" -f ($Size / 1GB)
    }
    elseif ($Size -gt 1MB) {
        return "{0:N2} MB" -f ($Size / 1MB)
    }
    else {
        return "{0:N2} KB" -f ($Size / 1KB)
    }
}

# Check disk space
function Get-DiskSpace {
    Write-ColorText "=== C Disk Space Detection ===" "Cyan"
    $disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "C:"}
    $totalSize = $disk.Size
    $freeSpace = $disk.FreeSpace
    $usedSpace = $totalSize - $freeSpace
    $percentFree = ($freeSpace / $totalSize) * 100
    
    Write-ColorText "Total Size: $(Format-Size $totalSize)" "White"
    Write-ColorText "Used Space: $(Format-Size $usedSpace)" "Yellow"
    Write-ColorText "Free Space: $(Format-Size $freeSpace)" "Green"
    Write-ColorText "Free Percentage: {0:N2}%" -f $percentFree "Green"
    
    if ($percentFree -lt 10) {
        Write-ColorText "WARNING: C disk space less than 10%, cleanup recommended!" "Red"
    }
    elseif ($percentFree -lt 20) {
        Write-ColorText "NOTICE: C disk space less than 20%, cleanup suggested" "Yellow"
    }
    
    return @{
        TotalSize = $totalSize
        FreeSpace = $freeSpace
        UsedSpace = $usedSpace
        PercentFree = $percentFree
    }
}

# Check large folders
function Get-LargeFolders {
    Write-ColorText "`n=== Checking Main Folder Usage ===" "Cyan"
    
    $folders = @("ProgramData", "Users", "Windows", "Program Files", "Program Files (x86)")
    $results = @()
    
    foreach ($folder in $folders) {
        $path = "C:\$folder"
        if (Test-Path $path) {
            Write-ColorText "Checking $folder..." "Gray"
            $size = Get-FolderSize $path
            $results += [PSCustomObject]@{
                Name = $folder
                Path = $path
                Size = $size
                SizeFormatted = Format-Size $size
            }
        }
    }
    
    $results | Sort-Object Size -Descending | Format-Table Name, SizeFormatted -AutoSize
    return $results
}

# Check ProgramData details
function Get-ProgramDataDetails {
    Write-ColorText "`n=== ProgramData Detailed Analysis ===" "Cyan"
    
    $programDataPath = "C:\ProgramData"
    $folders = Get-ChildItem $programDataPath -Directory -ErrorAction SilentlyContinue
    $results = @()
    
    foreach ($folder in $folders) {
        Write-ColorText "Checking $($folder.Name)..." "Gray"
        $size = Get-FolderSize $folder.FullName
        if ($size -gt 100MB) {
            $results += [PSCustomObject]@{
                Name = $folder.Name
                Size = $size
                SizeFormatted = Format-Size $size
            }
        }
    }
    
    $results | Sort-Object Size -Descending | Format-Table Name, SizeFormatted -AutoSize
    return $results
}

# Clean temp files
function Clear-TempFiles {
    Write-ColorText "`n=== Cleaning Temp Files ===" "Cyan"
    
    $tempPaths = @(
        $env:TEMP,
        "C:\Windows\Temp",
        "C:\ProgramData\temp"
    )
    
    $totalCleaned = 0
    
    foreach ($tempPath in $tempPaths) {
        if (Test-Path $tempPath) {
            Write-ColorText "Cleaning $tempPath..." "Yellow"
            try {
                $beforeSize = Get-FolderSize $tempPath
                Get-ChildItem $tempPath -Recurse -ErrorAction SilentlyContinue | 
                    Remove-Item -Force -Recurse -ErrorAction SilentlyContinue
                $afterSize = Get-FolderSize $tempPath
                $cleaned = $beforeSize - $afterSize
                $totalCleaned += $cleaned
                Write-ColorText "Freed space: $(Format-Size $cleaned)" "Green"
            }
            catch {
                Write-ColorText "Error cleaning $tempPath" "Red"
            }
        }
    }
    
    Write-ColorText "Total temp files freed: $(Format-Size $totalCleaned)" "Green"
    return $totalCleaned
}

# Clean Package Cache
function Clear-PackageCache {
    Write-ColorText "`n=== Cleaning Package Cache ===" "Cyan"
    
    $packageCachePath = "C:\ProgramData\Package Cache"
    if (Test-Path $packageCachePath) {
        try {
            $beforeSize = Get-FolderSize $packageCachePath
            Write-ColorText "Size before cleanup: $(Format-Size $beforeSize)" "Yellow"
            
            Remove-Item "$packageCachePath\*" -Recurse -Force -ErrorAction SilentlyContinue
            
            $afterSize = Get-FolderSize $packageCachePath
            $cleaned = $beforeSize - $afterSize
            Write-ColorText "Freed space: $(Format-Size $cleaned)" "Green"
            return $cleaned
        }
        catch {
            Write-ColorText "Error cleaning Package Cache" "Red"
            return 0
        }
    }
    else {
        Write-ColorText "Package Cache directory not found" "Gray"
        return 0
    }
}

# Clean conda cache
function Clear-CondaCache {
    Write-ColorText "`n=== Cleaning Conda Cache ===" "Cyan"
    
    try {
        $condaVersion = conda --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "Found Conda environment, cleaning cache..." "Yellow"
            conda clean --all --yes 2>$null
            Write-ColorText "Conda cache cleanup completed" "Green"
        }
        else {
            Write-ColorText "Conda environment not found, skipping" "Gray"
        }
    }
    catch {
        Write-ColorText "Error cleaning Conda cache" "Red"
    }
}

# Clean Windows Update cache
function Clear-WindowsUpdateCache {
    Write-ColorText "`n=== Cleaning Windows Update Cache ===" "Cyan"
    
    $updateCachePath = "C:\Windows\SoftwareDistribution\Download"
    if (Test-Path $updateCachePath) {
        try {
            $beforeSize = Get-FolderSize $updateCachePath
            Write-ColorText "Size before cleanup: $(Format-Size $beforeSize)" "Yellow"
            
            Stop-Service wuauserv -Force -ErrorAction SilentlyContinue
            Remove-Item "$updateCachePath\*" -Recurse -Force -ErrorAction SilentlyContinue
            Start-Service wuauserv -ErrorAction SilentlyContinue
            
            $afterSize = Get-FolderSize $updateCachePath
            $cleaned = $beforeSize - $afterSize
            Write-ColorText "Freed space: $(Format-Size $cleaned)" "Green"
            return $cleaned
        }
        catch {
            Write-ColorText "Error cleaning Windows Update cache" "Red"
            return 0
        }
    }
    else {
        Write-ColorText "Windows Update cache directory not found" "Gray"
        return 0
    }
}

# Clean system logs
function Clear-SystemLogs {
    Write-ColorText "`n=== Cleaning System Logs ===" "Cyan"

    try {
        $logCount = 0
        wevtutil el | ForEach-Object {
            wevtutil cl $_ 2>$null
            if ($LASTEXITCODE -eq 0) { $logCount++ }
        }
        Write-ColorText "Cleaned $logCount log files" "Green"
    }
    catch {
        Write-ColorText "Error cleaning system logs" "Red"
    }
}

# Execute all cleanup operations
function Start-CleanupProcess {
    param([bool]$AutoConfirm = $false)

    Write-ColorText "`n=== Starting Cleanup Process ===" "Cyan"

    if (-not $AutoConfirm) {
        $confirm = Read-Host "Are you sure to start cleanup? This will delete temp files and caches. (y/N)"
        if ($confirm -ne 'y' -and $confirm -ne 'Y') {
            Write-ColorText "Cleanup cancelled" "Yellow"
            return
        }
    }

    $totalCleaned = 0

    # Record space before cleanup
    $beforeCleanup = Get-DiskSpace

    # Execute cleanup operations
    $totalCleaned += Clear-TempFiles
    $totalCleaned += Clear-PackageCache
    Clear-CondaCache
    $totalCleaned += Clear-WindowsUpdateCache
    Clear-SystemLogs

    # Record space after cleanup
    Write-ColorText "`n=== Cleanup Completed, Checking Results ===" "Cyan"
    $afterCleanup = Get-DiskSpace

    $actualFreed = $afterCleanup.FreeSpace - $beforeCleanup.FreeSpace

    Write-ColorText "`n=== Cleanup Summary ===" "Green"
    Write-ColorText "Estimated freed space: $(Format-Size $totalCleaned)" "White"
    Write-ColorText "Actually freed space: $(Format-Size $actualFreed)" "Green"
    Write-ColorText "Free space before: $(Format-Size $beforeCleanup.FreeSpace)" "White"
    Write-ColorText "Free space after: $(Format-Size $afterCleanup.FreeSpace)" "Green"
}

# Main program entry
function Main {
    Clear-Host
    Write-ColorText "C Disk Space Cleanup Tool v1.0" "Cyan"
    Write-ColorText "Author: Augment Agent" "Gray"
    Write-ColorText "=" * 50 "Cyan"

    # Check if running as administrator
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
    if (-not $isAdmin) {
        Write-ColorText "Recommend running as administrator for best cleanup results" "Yellow"
    }

    # Execute detection
    $diskInfo = Get-DiskSpace
    $largeFolders = Get-LargeFolders
    $programDataDetails = Get-ProgramDataDetails

    # Decide next action based on parameters
    if ($CheckOnly) {
        Write-ColorText "`nDetection completed. Use -AutoClean parameter for auto cleanup." "Green"
        return
    }

    if ($AutoClean) {
        Start-CleanupProcess -AutoConfirm $true
    }
    else {
        Write-ColorText "`nDo you want to start cleanup process?" "Yellow"
        Write-ColorText "1. Start cleanup" "White"
        Write-ColorText "2. View detection results only" "White"
        Write-ColorText "3. Exit" "White"

        $choice = Read-Host "Please choose (1-3)"

        switch ($choice) {
            "1" { Start-CleanupProcess }
            "2" { Write-ColorText "Detection results shown above" "Green" }
            "3" { Write-ColorText "Goodbye!" "Green" }
            default { Write-ColorText "Invalid choice, exiting" "Red" }
        }
    }
}

# Run main program
try {
    Main
}
catch {
    Write-ColorText "Program execution error" "Red"
    Write-ColorText "Please run this script as administrator" "Yellow"
}

Write-ColorText "`nPress any key to exit..." "Gray"
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
