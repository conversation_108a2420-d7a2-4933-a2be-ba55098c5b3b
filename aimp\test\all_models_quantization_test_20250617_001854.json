{"device": "cpu", "file_status": {"det_model": true, "cls_model": true, "cls_vertical_model": true, "rec_model": true, "layout_model": true, "stain_model": true, "det0789_model": true, "kp_model": true, "pe_model": true, "v69_model": true}, "model_results": {"det_model": {"original": {"model_name": "det_model", "quantization_enabled": false, "device": "cpu", "success": true, "load_time": 0.24112749099731445, "inference_time": 0, "error": null}, "quantized": {"model_name": "det_model", "quantization_enabled": true, "device": "cpu", "success": true, "load_time": 0.0, "inference_time": 0, "error": null}, "comparison": {"load_speedup": 1, "load_time_saved": 0.24112749099731445}}, "cls_model": {"original": {"model_name": "cls_model", "quantization_enabled": false, "device": "cpu", "success": true, "load_time": 0.20200276374816895, "inference_time": 0, "error": null}, "quantized": {"model_name": "cls_model", "quantization_enabled": true, "device": "cpu", "success": true, "load_time": 0.0, "inference_time": 0, "error": null}, "comparison": {"load_speedup": 1, "load_time_saved": 0.20200276374816895}}, "cls_vertical_model": {"original": {"model_name": "cls_vertical_model", "quantization_enabled": false, "device": "cpu", "success": true, "load_time": 0.20593929290771484, "inference_time": 0, "error": null}, "quantized": {"model_name": "cls_vertical_model", "quantization_enabled": true, "device": "cpu", "success": true, "load_time": 0.0, "inference_time": 0, "error": null}, "comparison": {"load_speedup": 1, "load_time_saved": 0.20593929290771484}}, "rec_model": {"original": {"model_name": "rec_model", "quantization_enabled": false, "device": "cpu", "success": true, "load_time": 0.21211910247802734, "inference_time": 0, "error": null}, "quantized": {"model_name": "rec_model", "quantization_enabled": true, "device": "cpu", "success": true, "load_time": 0.0, "inference_time": 0, "error": null}, "comparison": {"load_speedup": 1, "load_time_saved": 0.21211910247802734}}, "layout_model": {"original": {"model_name": "layout_model", "quantization_enabled": false, "device": "cpu", "success": false, "load_time": 0, "inference_time": 0, "error": "\nUnknown builtin op: torchvision::nms.\nCould not find any similar ops to torchvision::nms. This op may not exist or may not be currently supported in TorchScript.\n:\n  File \"/home/<USER>/.local/miniconda3/envs/flink/lib/python3.6/site-packages/torchvision/ops/boxes.py\", line 35\n    \"\"\"\n    _assert_has_ops()\n    return torch.ops.torchvision.nms(boxes, scores, iou_threshold)\n           ~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 148\n  _61 = __torch__.torchvision.extension._assert_has_ops\n  _62 = _61()\n  _63 = ops.torchvision.nms(boxes, scores, iou_threshold)\n        ~~~~~~~~~~~~~~~~~~~ <--- HERE\n  return _63\n'nms' is being compiled since it was called from '_batched_nms_vanilla'\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 77\n    _28 = torch.index(boxes, _27)\n    _29 = annotate(List[Optional[Tensor]], [curr_indices])\n    curr_keep_indices = __torch__.torchvision.ops.boxes.nms(_28, torch.index(scores, _29), iou_threshold, )\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    _30 = annotate(List[Optional[Tensor]], [curr_keep_indices])\n    _31 = torch.index(curr_indices, _30)\n'_batched_nms_vanilla' is being compiled since it was called from 'batched_nms'\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 35\n    idxs: Tensor,\n    iou_threshold: float) -> Tensor:\n  _9 = __torch__.torchvision.ops.boxes._batched_nms_vanilla\n  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n  _10 = __torch__.torchvision.ops.boxes._batched_nms_coordinate_trick\n  if torch.gt(torch.numel(boxes), 4000):\n'batched_nms' is being compiled since it was called from 'RegionProposalNetwork.filter_proposals'\nSerialized   File \"code/__torch__/torchvision/models/detection/rpn.py\", line 72\n    _11 = __torch__.torchvision.ops.boxes.clip_boxes_to_image\n    _12 = __torch__.torchvision.ops.boxes.remove_small_boxes\n    _13 = __torch__.torchvision.ops.boxes.batched_nms\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    num_images = (torch.size(proposals))[0]\n    device = ops.prim.device(proposals)\n'RegionProposalNetwork.filter_proposals' is being compiled since it was called from 'RegionProposalNetwork.forward'\n  File \"/home/<USER>/.local/miniconda3/envs/flink/lib/python3.6/site-packages/torchvision/models/detection/rpn.py\", line 356\n        proposals = self.box_coder.decode(pred_bbox_deltas.detach(), anchors)\n        proposals = proposals.view(num_images, -1, 4)\n        boxes, scores = self.filter_proposals(proposals, objectness, images.image_sizes, num_anchors_per_level)\n                        ~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    \n        losses = {}\nSerialized   File \"code/__torch__/torchvision/models/detection/rpn.py\", line 43\n    proposals0 = torch.view(proposals, [num_images, -1, 4])\n    image_sizes = images.image_sizes\n    _8 = (self).filter_proposals(proposals0, objectness0, image_sizes, num_anchors_per_level, )\n                                                                       ~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    boxes, scores, = _8\n    losses = annotate(Dict[str, Tensor], {})\n"}, "quantized": {"model_name": "layout_model", "quantization_enabled": true, "device": "cpu", "success": false, "load_time": 0, "inference_time": 0, "error": "\nUnknown builtin op: torchvision::nms.\nCould not find any similar ops to torchvision::nms. This op may not exist or may not be currently supported in TorchScript.\n:\n  File \"/home/<USER>/.local/miniconda3/envs/flink/lib/python3.6/site-packages/torchvision/ops/boxes.py\", line 35\n    \"\"\"\n    _assert_has_ops()\n    return torch.ops.torchvision.nms(boxes, scores, iou_threshold)\n           ~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 148\n  _61 = __torch__.torchvision.extension._assert_has_ops\n  _62 = _61()\n  _63 = ops.torchvision.nms(boxes, scores, iou_threshold)\n        ~~~~~~~~~~~~~~~~~~~ <--- HERE\n  return _63\n'nms' is being compiled since it was called from '_batched_nms_vanilla'\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 77\n    _28 = torch.index(boxes, _27)\n    _29 = annotate(List[Optional[Tensor]], [curr_indices])\n    curr_keep_indices = __torch__.torchvision.ops.boxes.nms(_28, torch.index(scores, _29), iou_threshold, )\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    _30 = annotate(List[Optional[Tensor]], [curr_keep_indices])\n    _31 = torch.index(curr_indices, _30)\n'_batched_nms_vanilla' is being compiled since it was called from 'batched_nms'\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 35\n    idxs: Tensor,\n    iou_threshold: float) -> Tensor:\n  _9 = __torch__.torchvision.ops.boxes._batched_nms_vanilla\n  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n  _10 = __torch__.torchvision.ops.boxes._batched_nms_coordinate_trick\n  if torch.gt(torch.numel(boxes), 4000):\n'batched_nms' is being compiled since it was called from 'RegionProposalNetwork.filter_proposals'\nSerialized   File \"code/__torch__/torchvision/models/detection/rpn.py\", line 72\n    _11 = __torch__.torchvision.ops.boxes.clip_boxes_to_image\n    _12 = __torch__.torchvision.ops.boxes.remove_small_boxes\n    _13 = __torch__.torchvision.ops.boxes.batched_nms\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    num_images = (torch.size(proposals))[0]\n    device = ops.prim.device(proposals)\n'RegionProposalNetwork.filter_proposals' is being compiled since it was called from 'RegionProposalNetwork.forward'\n  File \"/home/<USER>/.local/miniconda3/envs/flink/lib/python3.6/site-packages/torchvision/models/detection/rpn.py\", line 356\n        proposals = self.box_coder.decode(pred_bbox_deltas.detach(), anchors)\n        proposals = proposals.view(num_images, -1, 4)\n        boxes, scores = self.filter_proposals(proposals, objectness, images.image_sizes, num_anchors_per_level)\n                        ~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    \n        losses = {}\nSerialized   File \"code/__torch__/torchvision/models/detection/rpn.py\", line 43\n    proposals0 = torch.view(proposals, [num_images, -1, 4])\n    image_sizes = images.image_sizes\n    _8 = (self).filter_proposals(proposals0, objectness0, image_sizes, num_anchors_per_level, )\n                                                                       ~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    boxes, scores, = _8\n    losses = annotate(Dict[str, Tensor], {})\n"}}, "stain_model": {"original": {"model_name": "stain_model", "quantization_enabled": false, "device": "cpu", "success": false, "load_time": 0, "inference_time": 0, "error": "\nUnknown builtin op: torchvision::nms.\nCould not find any similar ops to torchvision::nms. This op may not exist or may not be currently supported in TorchScript.\n:\n  File \"/home/<USER>/.local/miniconda3/envs/flink/lib/python3.6/site-packages/torchvision/ops/boxes.py\", line 35\n    \"\"\"\n    _assert_has_ops()\n    return torch.ops.torchvision.nms(boxes, scores, iou_threshold)\n           ~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 148\n  _61 = __torch__.torchvision.extension._assert_has_ops\n  _62 = _61()\n  _63 = ops.torchvision.nms(boxes, scores, iou_threshold)\n        ~~~~~~~~~~~~~~~~~~~ <--- HERE\n  return _63\n'nms' is being compiled since it was called from '_batched_nms_vanilla'\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 77\n    _28 = torch.index(boxes, _27)\n    _29 = annotate(List[Optional[Tensor]], [curr_indices])\n    curr_keep_indices = __torch__.torchvision.ops.boxes.nms(_28, torch.index(scores, _29), iou_threshold, )\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    _30 = annotate(List[Optional[Tensor]], [curr_keep_indices])\n    _31 = torch.index(curr_indices, _30)\n'_batched_nms_vanilla' is being compiled since it was called from 'batched_nms'\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 35\n    idxs: Tensor,\n    iou_threshold: float) -> Tensor:\n  _9 = __torch__.torchvision.ops.boxes._batched_nms_vanilla\n  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n  _10 = __torch__.torchvision.ops.boxes._batched_nms_coordinate_trick\n  if torch.gt(torch.numel(boxes), 4000):\n'batched_nms' is being compiled since it was called from 'RegionProposalNetwork.filter_proposals'\nSerialized   File \"code/__torch__/torchvision/models/detection/rpn.py\", line 72\n    _11 = __torch__.torchvision.ops.boxes.clip_boxes_to_image\n    _12 = __torch__.torchvision.ops.boxes.remove_small_boxes\n    _13 = __torch__.torchvision.ops.boxes.batched_nms\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    num_images = (torch.size(proposals))[0]\n    device = ops.prim.device(proposals)\n'RegionProposalNetwork.filter_proposals' is being compiled since it was called from 'RegionProposalNetwork.forward'\n  File \"/home/<USER>/.local/miniconda3/envs/flink/lib/python3.6/site-packages/torchvision/models/detection/rpn.py\", line 356\n        proposals = self.box_coder.decode(pred_bbox_deltas.detach(), anchors)\n        proposals = proposals.view(num_images, -1, 4)\n        boxes, scores = self.filter_proposals(proposals, objectness, images.image_sizes, num_anchors_per_level)\n                        ~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    \n        losses = {}\nSerialized   File \"code/__torch__/torchvision/models/detection/rpn.py\", line 43\n    proposals0 = torch.view(proposals, [num_images, -1, 4])\n    image_sizes = images.image_sizes\n    _8 = (self).filter_proposals(proposals0, objectness0, image_sizes, num_anchors_per_level, )\n                                                                       ~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    boxes, scores, = _8\n    losses = annotate(Dict[str, Tensor], {})\n"}, "quantized": {"model_name": "stain_model", "quantization_enabled": true, "device": "cpu", "success": false, "load_time": 0, "inference_time": 0, "error": "\nUnknown builtin op: torchvision::nms.\nCould not find any similar ops to torchvision::nms. This op may not exist or may not be currently supported in TorchScript.\n:\n  File \"/home/<USER>/.local/miniconda3/envs/flink/lib/python3.6/site-packages/torchvision/ops/boxes.py\", line 35\n    \"\"\"\n    _assert_has_ops()\n    return torch.ops.torchvision.nms(boxes, scores, iou_threshold)\n           ~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 148\n  _61 = __torch__.torchvision.extension._assert_has_ops\n  _62 = _61()\n  _63 = ops.torchvision.nms(boxes, scores, iou_threshold)\n        ~~~~~~~~~~~~~~~~~~~ <--- HERE\n  return _63\n'nms' is being compiled since it was called from '_batched_nms_vanilla'\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 77\n    _28 = torch.index(boxes, _27)\n    _29 = annotate(List[Optional[Tensor]], [curr_indices])\n    curr_keep_indices = __torch__.torchvision.ops.boxes.nms(_28, torch.index(scores, _29), iou_threshold, )\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    _30 = annotate(List[Optional[Tensor]], [curr_keep_indices])\n    _31 = torch.index(curr_indices, _30)\n'_batched_nms_vanilla' is being compiled since it was called from 'batched_nms'\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 35\n    idxs: Tensor,\n    iou_threshold: float) -> Tensor:\n  _9 = __torch__.torchvision.ops.boxes._batched_nms_vanilla\n  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n  _10 = __torch__.torchvision.ops.boxes._batched_nms_coordinate_trick\n  if torch.gt(torch.numel(boxes), 4000):\n'batched_nms' is being compiled since it was called from 'RegionProposalNetwork.filter_proposals'\nSerialized   File \"code/__torch__/torchvision/models/detection/rpn.py\", line 72\n    _11 = __torch__.torchvision.ops.boxes.clip_boxes_to_image\n    _12 = __torch__.torchvision.ops.boxes.remove_small_boxes\n    _13 = __torch__.torchvision.ops.boxes.batched_nms\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    num_images = (torch.size(proposals))[0]\n    device = ops.prim.device(proposals)\n'RegionProposalNetwork.filter_proposals' is being compiled since it was called from 'RegionProposalNetwork.forward'\n  File \"/home/<USER>/.local/miniconda3/envs/flink/lib/python3.6/site-packages/torchvision/models/detection/rpn.py\", line 356\n        proposals = self.box_coder.decode(pred_bbox_deltas.detach(), anchors)\n        proposals = proposals.view(num_images, -1, 4)\n        boxes, scores = self.filter_proposals(proposals, objectness, images.image_sizes, num_anchors_per_level)\n                        ~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    \n        losses = {}\nSerialized   File \"code/__torch__/torchvision/models/detection/rpn.py\", line 43\n    proposals0 = torch.view(proposals, [num_images, -1, 4])\n    image_sizes = images.image_sizes\n    _8 = (self).filter_proposals(proposals0, objectness0, image_sizes, num_anchors_per_level, )\n                                                                       ~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    boxes, scores, = _8\n    losses = annotate(Dict[str, Tensor], {})\n"}}, "det0789_model": {"original": {"model_name": "det0789_model", "quantization_enabled": false, "device": "cpu", "success": false, "load_time": 0, "inference_time": 0, "error": "\nUnknown builtin op: torchvision::nms.\nCould not find any similar ops to torchvision::nms. This op may not exist or may not be currently supported in TorchScript.\n:\n  File \"/home/<USER>/.local/miniconda3/envs/flink/lib/python3.6/site-packages/torchvision/ops/boxes.py\", line 35\n    \"\"\"\n    _assert_has_ops()\n    return torch.ops.torchvision.nms(boxes, scores, iou_threshold)\n           ~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 148\n  _61 = __torch__.torchvision.extension._assert_has_ops\n  _62 = _61()\n  _63 = ops.torchvision.nms(boxes, scores, iou_threshold)\n        ~~~~~~~~~~~~~~~~~~~ <--- HERE\n  return _63\n'nms' is being compiled since it was called from '_batched_nms_vanilla'\n  File \"/home/<USER>/.local/miniconda3/envs/flink/lib/python3.6/site-packages/torchvision/ops/boxes.py\", line 102\n    for class_id in torch.unique(idxs):\n        curr_indices = torch.where(idxs == class_id)[0]\n        curr_keep_indices = nms(boxes[curr_indices], scores[curr_indices], iou_threshold)\n                            ~~~ <--- HERE\n        keep_mask[curr_indices[curr_keep_indices]] = True\n    keep_indices = torch.where(keep_mask)[0]\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 77\n    _28 = torch.index(boxes, _27)\n    _29 = annotate(List[Optional[Tensor]], [curr_indices])\n    curr_keep_indices = __torch__.torchvision.ops.boxes.nms(_28, torch.index(scores, _29), iou_threshold, )\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    _30 = annotate(List[Optional[Tensor]], [curr_keep_indices])\n    _31 = torch.index(curr_indices, _30)\n'_batched_nms_vanilla' is being compiled since it was called from 'batched_nms'\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 35\n    idxs: Tensor,\n    iou_threshold: float) -> Tensor:\n  _9 = __torch__.torchvision.ops.boxes._batched_nms_vanilla\n  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n  _10 = __torch__.torchvision.ops.boxes._batched_nms_coordinate_trick\n  if torch.gt(torch.numel(boxes), 4000):\n'batched_nms' is being compiled since it was called from 'RegionProposalNetwork.filter_proposals'\nSerialized   File \"code/__torch__/torchvision/models/detection/rpn.py\", line 72\n    _11 = __torch__.torchvision.ops.boxes.clip_boxes_to_image\n    _12 = __torch__.torchvision.ops.boxes.remove_small_boxes\n    _13 = __torch__.torchvision.ops.boxes.batched_nms\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    num_images = (torch.size(proposals))[0]\n    device = ops.prim.device(proposals)\n'RegionProposalNetwork.filter_proposals' is being compiled since it was called from 'RegionProposalNetwork.forward'\n  File \"/home/<USER>/.local/miniconda3/envs/flink/lib/python3.6/site-packages/torchvision/models/detection/rpn.py\", line 356\n        proposals = self.box_coder.decode(pred_bbox_deltas.detach(), anchors)\n        proposals = proposals.view(num_images, -1, 4)\n        boxes, scores = self.filter_proposals(proposals, objectness, images.image_sizes, num_anchors_per_level)\n                        ~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    \n        losses = {}\nSerialized   File \"code/__torch__/torchvision/models/detection/rpn.py\", line 43\n    proposals0 = torch.view(proposals, [num_images, -1, 4])\n    image_sizes = images.image_sizes\n    _8 = (self).filter_proposals(proposals0, objectness0, image_sizes, num_anchors_per_level, )\n                                                                       ~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    boxes, scores, = _8\n    losses = annotate(Dict[str, Tensor], {})\n"}, "quantized": {"model_name": "det0789_model", "quantization_enabled": true, "device": "cpu", "success": false, "load_time": 0, "inference_time": 0, "error": "\nUnknown builtin op: torchvision::nms.\nCould not find any similar ops to torchvision::nms. This op may not exist or may not be currently supported in TorchScript.\n:\n  File \"/home/<USER>/.local/miniconda3/envs/flink/lib/python3.6/site-packages/torchvision/ops/boxes.py\", line 35\n    \"\"\"\n    _assert_has_ops()\n    return torch.ops.torchvision.nms(boxes, scores, iou_threshold)\n           ~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 148\n  _61 = __torch__.torchvision.extension._assert_has_ops\n  _62 = _61()\n  _63 = ops.torchvision.nms(boxes, scores, iou_threshold)\n        ~~~~~~~~~~~~~~~~~~~ <--- HERE\n  return _63\n'nms' is being compiled since it was called from '_batched_nms_vanilla'\n  File \"/home/<USER>/.local/miniconda3/envs/flink/lib/python3.6/site-packages/torchvision/ops/boxes.py\", line 102\n    for class_id in torch.unique(idxs):\n        curr_indices = torch.where(idxs == class_id)[0]\n        curr_keep_indices = nms(boxes[curr_indices], scores[curr_indices], iou_threshold)\n                            ~~~ <--- HERE\n        keep_mask[curr_indices[curr_keep_indices]] = True\n    keep_indices = torch.where(keep_mask)[0]\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 77\n    _28 = torch.index(boxes, _27)\n    _29 = annotate(List[Optional[Tensor]], [curr_indices])\n    curr_keep_indices = __torch__.torchvision.ops.boxes.nms(_28, torch.index(scores, _29), iou_threshold, )\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    _30 = annotate(List[Optional[Tensor]], [curr_keep_indices])\n    _31 = torch.index(curr_indices, _30)\n'_batched_nms_vanilla' is being compiled since it was called from 'batched_nms'\nSerialized   File \"code/__torch__/torchvision/ops/boxes.py\", line 35\n    idxs: Tensor,\n    iou_threshold: float) -> Tensor:\n  _9 = __torch__.torchvision.ops.boxes._batched_nms_vanilla\n  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n  _10 = __torch__.torchvision.ops.boxes._batched_nms_coordinate_trick\n  if torch.gt(torch.numel(boxes), 4000):\n'batched_nms' is being compiled since it was called from 'RegionProposalNetwork.filter_proposals'\nSerialized   File \"code/__torch__/torchvision/models/detection/rpn.py\", line 72\n    _11 = __torch__.torchvision.ops.boxes.clip_boxes_to_image\n    _12 = __torch__.torchvision.ops.boxes.remove_small_boxes\n    _13 = __torch__.torchvision.ops.boxes.batched_nms\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    num_images = (torch.size(proposals))[0]\n    device = ops.prim.device(proposals)\n'RegionProposalNetwork.filter_proposals' is being compiled since it was called from 'RegionProposalNetwork.forward'\n  File \"/home/<USER>/.local/miniconda3/envs/flink/lib/python3.6/site-packages/torchvision/models/detection/rpn.py\", line 356\n        proposals = self.box_coder.decode(pred_bbox_deltas.detach(), anchors)\n        proposals = proposals.view(num_images, -1, 4)\n        boxes, scores = self.filter_proposals(proposals, objectness, images.image_sizes, num_anchors_per_level)\n                        ~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    \n        losses = {}\nSerialized   File \"code/__torch__/torchvision/models/detection/rpn.py\", line 43\n    proposals0 = torch.view(proposals, [num_images, -1, 4])\n    image_sizes = images.image_sizes\n    _8 = (self).filter_proposals(proposals0, objectness0, image_sizes, num_anchors_per_level, )\n                                                                       ~~~~~~~~~~~~~~~~~~~~~ <--- HERE\n    boxes, scores, = _8\n    losses = annotate(Dict[str, Tensor], {})\n"}}, "kp_model": {"original": {"model_name": "kp_model", "quantization_enabled": false, "device": "cpu", "success": true, "load_time": 2.0261168479919434, "inference_time": 0, "error": null}, "quantized": {"model_name": "kp_model", "quantization_enabled": true, "device": "cpu", "success": true, "load_time": 0.0, "inference_time": 0, "error": null}, "comparison": {"load_speedup": 1, "load_time_saved": 2.0261168479919434}}, "pe_model": {"original": {"model_name": "pe_model", "quantization_enabled": false, "device": "cpu", "success": true, "load_time": 0.4802711009979248, "inference_time": 0, "error": null}, "quantized": {"model_name": "pe_model", "quantization_enabled": true, "device": "cpu", "success": true, "load_time": 0.0, "inference_time": 0, "error": null}, "comparison": {"load_speedup": 1, "load_time_saved": 0.4802711009979248}}, "v69_model": {"original": {"model_name": "v69_model", "quantization_enabled": false, "device": "cpu", "success": true, "load_time": 3.804246664047241, "inference_time": 0, "error": null}, "quantized": {"model_name": "v69_model", "quantization_enabled": true, "device": "cpu", "success": true, "load_time": 0.0, "inference_time": 0, "error": null}, "comparison": {"load_speedup": 1, "load_time_saved": 3.804246664047241}}}, "summary": {"total_models": 10, "successful_quantizations": 7, "average_load_speedup": 1.0, "average_inference_speedup": 0, "total_load_time_saved": 7.171823263168335, "total_inference_time_saved": 0}}