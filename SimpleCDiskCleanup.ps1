# Simple C Disk Cleanup Tool
# Author: Augment Agent

Write-Host "=== C Disk Space Cleanup Tool ===" -ForegroundColor Cyan
Write-Host "Author: Augment Agent" -ForegroundColor Gray
Write-Host "=" * 50 -ForegroundColor Cyan

# Function to format file size
function Format-FileSize {
    param([long]$Size)
    if ($Size -gt 1GB) {
        return "{0:N2} GB" -f ($Size / 1GB)
    }
    elseif ($Size -gt 1MB) {
        return "{0:N2} MB" -f ($Size / 1MB)
    }
    else {
        return "{0:N2} KB" -f ($Size / 1KB)
    }
}

# Function to get folder size safely
function Get-SafeFolderSize {
    param([string]$Path)
    try {
        if (Test-Path $Path) {
            $size = (Get-ChildItem $Path -Recurse -File -ErrorAction SilentlyContinue | 
                    Measure-Object -Property Length -Sum).Sum
            if ($size -eq $null) { return 0 }
            return $size
        }
        return 0
    }
    catch {
        return 0
    }
}

# Check current disk space
Write-Host "`n=== Current C Disk Space ===" -ForegroundColor Cyan
try {
    $disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "C:"}
    $totalSize = $disk.Size
    $freeSpace = $disk.FreeSpace
    $usedSpace = $totalSize - $freeSpace
    $percentFree = ($freeSpace / $totalSize) * 100
    
    Write-Host "Total Size: $(Format-FileSize $totalSize)" -ForegroundColor White
    Write-Host "Used Space: $(Format-FileSize $usedSpace)" -ForegroundColor Yellow
    Write-Host "Free Space: $(Format-FileSize $freeSpace)" -ForegroundColor Green
    Write-Host "Free Percentage: $([math]::Round($percentFree,2))%" -ForegroundColor Green
    
    if ($percentFree -lt 10) {
        Write-Host "WARNING: C disk space less than 10%!" -ForegroundColor Red
    }
    elseif ($percentFree -lt 20) {
        Write-Host "NOTICE: C disk space less than 20%" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "Error checking disk space: $($_.Exception.Message)" -ForegroundColor Red
}

# Check main folders
Write-Host "`n=== Main Folder Usage ===" -ForegroundColor Cyan
$mainFolders = @("ProgramData", "Users", "Windows", "Program Files", "Program Files (x86)")
foreach ($folder in $mainFolders) {
    $path = "C:\$folder"
    if (Test-Path $path) {
        $size = Get-SafeFolderSize $path
        Write-Host "$folder : $(Format-FileSize $size)" -ForegroundColor White
    }
}

# Check ProgramData details
Write-Host "`n=== ProgramData Details (>100MB) ===" -ForegroundColor Cyan
try {
    $programDataPath = "C:\ProgramData"
    $folders = Get-ChildItem $programDataPath -Directory -ErrorAction SilentlyContinue
    foreach ($folder in $folders) {
        $size = Get-SafeFolderSize $folder.FullName
        if ($size -gt 100MB) {
            Write-Host "$($folder.Name) : $(Format-FileSize $size)" -ForegroundColor White
        }
    }
}
catch {
    Write-Host "Error checking ProgramData: $($_.Exception.Message)" -ForegroundColor Red
}

# Ask user if they want to clean
Write-Host "`n=== Cleanup Options ===" -ForegroundColor Yellow
Write-Host "1. Clean temp files and caches" -ForegroundColor White
Write-Host "2. Just show results (no cleanup)" -ForegroundColor White
Write-Host "3. Exit" -ForegroundColor White

$choice = Read-Host "Please choose (1-3)"

if ($choice -eq "1") {
    Write-Host "`n=== Starting Cleanup ===" -ForegroundColor Cyan
    $totalCleaned = 0
    
    # Clean temp files
    Write-Host "Cleaning temp files..." -ForegroundColor Yellow
    $tempPaths = @($env:TEMP, "C:\Windows\Temp", "C:\ProgramData\temp")
    
    foreach ($tempPath in $tempPaths) {
        if (Test-Path $tempPath) {
            try {
                $beforeSize = Get-SafeFolderSize $tempPath
                Get-ChildItem $tempPath -Recurse -ErrorAction SilentlyContinue | 
                    Remove-Item -Force -Recurse -ErrorAction SilentlyContinue
                $afterSize = Get-SafeFolderSize $tempPath
                $cleaned = $beforeSize - $afterSize
                $totalCleaned += $cleaned
                Write-Host "Cleaned $tempPath : $(Format-FileSize $cleaned)" -ForegroundColor Green
            }
            catch {
                Write-Host "Error cleaning $tempPath" -ForegroundColor Red
            }
        }
    }
    
    # Clean Package Cache
    Write-Host "Cleaning Package Cache..." -ForegroundColor Yellow
    $packageCachePath = "C:\ProgramData\Package Cache"
    if (Test-Path $packageCachePath) {
        try {
            $beforeSize = Get-SafeFolderSize $packageCachePath
            Remove-Item "$packageCachePath\*" -Recurse -Force -ErrorAction SilentlyContinue
            $afterSize = Get-SafeFolderSize $packageCachePath
            $cleaned = $beforeSize - $afterSize
            $totalCleaned += $cleaned
            Write-Host "Cleaned Package Cache : $(Format-FileSize $cleaned)" -ForegroundColor Green
        }
        catch {
            Write-Host "Error cleaning Package Cache" -ForegroundColor Red
        }
    }
    
    # Clean conda cache if available
    Write-Host "Checking Conda cache..." -ForegroundColor Yellow
    try {
        $condaCheck = conda --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Cleaning Conda cache..." -ForegroundColor Yellow
            conda clean --all --yes 2>$null
            Write-Host "Conda cache cleaned" -ForegroundColor Green
        }
        else {
            Write-Host "Conda not found, skipping" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "Conda not available" -ForegroundColor Gray
    }
    
    # Clean Windows Update cache
    Write-Host "Cleaning Windows Update cache..." -ForegroundColor Yellow
    $updateCachePath = "C:\Windows\SoftwareDistribution\Download"
    if (Test-Path $updateCachePath) {
        try {
            $beforeSize = Get-SafeFolderSize $updateCachePath
            Stop-Service wuauserv -Force -ErrorAction SilentlyContinue
            Remove-Item "$updateCachePath\*" -Recurse -Force -ErrorAction SilentlyContinue
            Start-Service wuauserv -ErrorAction SilentlyContinue
            $afterSize = Get-SafeFolderSize $updateCachePath
            $cleaned = $beforeSize - $afterSize
            $totalCleaned += $cleaned
            Write-Host "Cleaned Windows Update cache : $(Format-FileSize $cleaned)" -ForegroundColor Green
        }
        catch {
            Write-Host "Error cleaning Windows Update cache" -ForegroundColor Red
        }
    }
    
    # Show final results
    Write-Host "`n=== Cleanup Summary ===" -ForegroundColor Green
    Write-Host "Total space freed: $(Format-FileSize $totalCleaned)" -ForegroundColor Green
    
    # Check disk space again
    Write-Host "`n=== Final Disk Space ===" -ForegroundColor Cyan
    try {
        $disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "C:"}
        $newFreeSpace = $disk.FreeSpace
        $newPercentFree = ($newFreeSpace / $disk.Size) * 100
        
        Write-Host "Free Space: $(Format-FileSize $newFreeSpace)" -ForegroundColor Green
        Write-Host "Free Percentage: $([math]::Round($newPercentFree,2))%" -ForegroundColor Green
    }
    catch {
        Write-Host "Error checking final disk space" -ForegroundColor Red
    }
}
elseif ($choice -eq "2") {
    Write-Host "Results shown above. No cleanup performed." -ForegroundColor Green
}
else {
    Write-Host "Exiting..." -ForegroundColor Green
}

Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = Read-Host
