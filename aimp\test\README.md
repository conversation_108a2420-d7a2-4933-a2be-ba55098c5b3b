# OCR模型量化测试

本目录包含OCR模型量化优化的测试脚本，用于验证量化对性能和精度的影响。

## 文件说明

### 测试脚本

- `quick_test.py` - 快速量化测试脚本，适合初步验证
- `test_quantization.py` - 完整的量化测试套件，包含详细的性能分析
- `README.md` - 本说明文件

### 量化策略

本项目采用混合精度量化策略：

- **文本检测模型** (`001-1.pt`) - INT8量化
- **文本分类模型** (`003.pt`, `006.pt`) - INT8量化  
- **文本识别模型** (`tp1t7.pt`) - FP16量化（保持精度）
- **版面分析模型** (`la_dp09-1.pt`) - INT8量化

## 使用方法

### 1. 快速测试

```bash
# 在aimp目录下运行
cd aimp
python test/quick_test.py
```

这个脚本会：
- 创建一个测试图片
- 分别测试原始模型和量化模型
- 对比性能和精度
- 评估是否达到每秒1张图片的目标

### 2. 完整测试

```bash
# 基本测试
python test/test_quantization.py

# 指定测试图片目录
python test/test_quantization.py --test-images /path/to/test/images

# 指定测试运行次数
python test/test_quantization.py --runs 5
```

完整测试会：
- 测试多张图片（如果提供）
- 进行多次运行取平均值
- 生成详细的性能报告
- 保存结果到JSON文件

## 测试结果解读

### 性能指标

- **速度提升倍数** - 量化模型相对原始模型的速度提升
- **平均处理时间** - 每张图片的平均处理时间
- **FPS** - 每秒处理图片数量
- **内存占用** - 模型加载后的内存使用

### 精度指标

- **文本匹配率** - 量化模型与原始模型识别结果的一致性
- **检测框数量** - 检测到的文本框数量对比
- **识别文本** - 具体的文字识别结果

## 预期结果

根据量化理论和实践经验，预期能够达到：

- **速度提升**: 2-3倍
- **内存减少**: 40-60%
- **精度损失**: <5%
- **目标性能**: 接近每秒1张图片

## 故障排除

### 常见问题

1. **模型文件不存在**
   ```
   解决方案：确保dtm目录下有所需的模型文件
   ```

2. **量化失败**
   ```
   解决方案：检查PyTorch版本，确保支持量化功能
   ```

3. **内存不足**
   ```
   解决方案：减少测试图片数量或降低图片分辨率
   ```

### 调试模式

在脚本中添加调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 环境要求

- Python 3.7+
- PyTorch 1.8+（支持量化）
- OpenCV
- NumPy
- PIL

## 开发说明

### 添加新的量化策略

在 `myocr.py` 的 `get_or_load` 方法中修改量化逻辑：

```python
def get_or_load(self, path):
    model = torch.jit.load(path, map_location=self.device)
    
    if self.device == 'cpu' and self.enable_quantization:
        # 在这里添加新的量化策略
        pass
    
    return model
```

### 添加新的测试指标

在测试脚本中扩展 `benchmark_ocr_system` 方法，添加更多性能指标。

## 版本历史

- v1.0 (2025-06-16) - 初始版本，支持基本的INT8和FP16量化测试

## 作者

- xubiyun (<EMAIL>)

## 许可证

内部项目，仅供开发测试使用。
