import time
from typing import Optional, <PERSON>, Callable, <PERSON><PERSON>, NamedTuple, Any
from enum import Enum
import os, re, base64, math, gc
from io import BytesIO
from functools import partial, update_wrapper
from urllib.parse import quote
from importlib_resources import files
import itertools, pathlib
from fastapi import (
    FastAPI, Depends, BackgroundTasks,
    Form, UploadFile, Query, Path, Body,
    HTTPException, Header, Request, Response, APIRouter
    )
from fastapi.datastructures import FormData, StarletteUploadFile
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.routing import APIRoute
from fastapi.openapi.docs import (
    get_swagger_ui_html,
    get_swagger_ui_oauth2_redirect_html,
)
from pydantic import (
    BaseModel, BaseSettings, Field, AnyHttpUrl, IPvAnyNetwork,
    conint, NonNegativeInt, Json, ValidationError,
    conlist, validator, DirectoryPath, FilePath,
    )
import asyncio
import contextlib
from threading import Lock, RLock
# from asgiref.sync import sync_to_async
from anyio import run_process
from cachetools import Cache, keys, _CacheInfo
import numpy as np
import cv2
cv2.setNumThreads(0)
from PIL import Image
from PIL.ImageOps import exif_transpose
from PIL.TiffImagePlugin import ImageFileDirectory_v2
Image.MAX_IMAGE_PIXELS = ********
os.environ['CUDA_MODULE_LOADING']='LAZY'
import torch, torchvision, torch.nn as nn, torch.nn.functional as F
import torchvision.transforms.functional as VTF
torch._C._jit_set_profiling_mode(False)
from myocr import OCRSystem
from zxingcpp import read_barcodes
import logging

logger = logging.getLogger(__name__)
# logger.parent =
# logging.getLogger('uvicorn')
# loggers = [logging.getLogger(name) for name in logging.root.manager.loggerDict]

class Tags(Enum):
    inspect = '图像审查'
    process = '图像处理'
    debug_inspect = '图像审查调试'
    debug_process = '图像处理调试'
    ocr = '光学字符识别'

app = FastAPI(
    docs_url=None,
    title='智慧文档图像能力接口',
    description='''文档图像合规审查及处理能力。

    软件在通用文书处理及一些行业场景得到生产验证，描述的功能可能不适用于其他图片。
    目前接口设计处于前期阶段，合规尤其需根据需求设计开放字段。
    能力基本完成，囊括生产在用的各项功能，可能缺少一些数据校验。
    文档部分响应示例未撰写，请试用接口获取示例。
    并发性质：目前需限制并发请求数防止显存等资源不足，一般会响应500代码，负载降低后请求正常。
    单张图像处理时，1660卡建议2~3张。此前在A4000显卡环境中，
    40并发图像处理引起系统内存数据错乱，表现为接口响应乱码。合规审查时可适当提高，如
    1660测试可接受10张并发。

    ''',
    contact={
        'name':  '研发中心 算法负责人',
    },
    responses={
        415: { 'description': '**img** 请求体参数上传的图片无法读取' }
    },
    openapi_tags=[
        {
            'name': Tags.inspect.value,
            'description': '''图像审查后台组，可创建给一张图像进行多个审查的接口并使用。

            对于合规审查各审查结果，约定：
            数值0、空数组或布尔假(false)表示没有问题，在容许范围内。
            非0数值、非空数组、布尔真(true)或其他响应为检出问题。
            ''',
        },
        {
            'name': Tags.debug_inspect.value,
            'description': '图像审查调试组，开发跟踪故障用，一些单点接口可能足够单项审查之用。',
        },
        {
            'name': Tags.process.value,
            'description': '''图像处理后台组，图像处理包含本软件特色工序工艺，作为一个整体开放接口。

            额外成功响应
            - 响应代码 304 无响应数据 : 图像处理经过判断，对 img 不做处理
            ''',
        },
        {
            'name': Tags.debug_process.value,
            'description': '图像处理调试组，开发跟踪故障用。',
        },
        {
            'name': Tags.ocr.value,
            'description': '光学字符识别组，提供OCR相关的功能接口。',
        },
    ])

def _cache(cache, typed=False):
    maxsize = cache.maxsize

    def decorator(func):
        key = keys.typedkey if typed else keys.hashkey
        hits = misses = 0
        lock = Lock()

        def wrapper(*args, **kwargs):
            nonlocal hits, misses
            k = key(*args, **kwargs)
            with lock:
                try:
                    v = cache[k]
                    hits += 1
                    return v
                except KeyError:
                    # print([k, args, kwargs])
                    misses += 1
                    v = func(*args, **kwargs)
                    try:
                        return cache.setdefault(k, v)
                    except ValueError:
                        return v  # value too large

        def cache_info():
            with lock:
                maxsize = cache.maxsize
                currsize = cache.currsize
            return _CacheInfo(hits, misses, maxsize, currsize)

        def cache_clear():
            nonlocal hits, misses
            with lock:
                try:
                    cache.clear()
                finally:
                    hits = misses = 0

        wrapper.cache_info = cache_info
        wrapper.cache_clear = cache_clear
        wrapper.cache_parameters = lambda: {"maxsize": maxsize, "typed": typed}
        update_wrapper(wrapper, func)
        return wrapper

    return decorator

_device_regex = r'^c(pu|uda)$'

class Settings(BaseSettings):
    dtm = files('dtm')
    det_model_path:FilePath = dtm/'001-1.pt'
    cls_model_path:FilePath = dtm/'003.pt'
    cls_vertical_model_path:FilePath = dtm/'006.pt'
    rec_model_path:FilePath = dtm/'tp1t7.pt'
    rec_char_dict_path:FilePath = dtm/'ppocr_keys_v1.txt'
    stain_model_path:FilePath = dtm/'dt36-1.pt'
    layout_model_path:FilePath = dtm/'la_dp09-1.pt'
    det0789_model_path:FilePath = dtm/'det0789.pt'
    kp_model_path:FilePath = dtm/'kp-180000-tf19-481.pt'
    pe_model_path:FilePath = dtm/'mlim2a.pt'
    v69_model_path:FilePath = dtm/'192-v69.pt'
    nlp_res_w2v_path: FilePath = dtm/'index.h5'

    def __hash__(self):
        return id(self)

@_cache(Cache(1))
def get_conf() -> Settings:
    return Settings()

_device_param = Query('cuda' if torch.cuda.is_available() else 'cpu', regex=_device_regex,
        description='使用的计算设备，cuda指GPU，cpu指CPU。若默认值为cpu，表示GPU不可用。')

@contextlib.contextmanager
def open_image(img: UploadFile):
    try:
        with Image.open(img.file, 'r') as f:
            yield f if f.getexif().get(274,1) == 1 else exif_transpose(f)
    except (Image.UnidentifiedImageError, Image.DecompressionBombError) as e:
        raise HTTPException(status_code=415, detail={
            'filename': img.filename, 'error': str(e)
            })

def read_image(img: UploadFile) -> np.ndarray:
    # print('read_image_start', time.time())
    with open_image(img) as f:
        a = cv2.cvtColor(np.asarray(f.convert('RGB')), cv2.COLOR_RGB2BGR)
        return a

_get_ocr_model_called = False
@_cache(Cache(1))
def get_ocr_model(conf: Settings = Depends(get_conf),
    device:str = _device_param):
    # print('type', type(device), device, id(conf))
    # print(get_conf.cache_info())
    # global _get_ocr_model_called; assert not _get_ocr_model_called; _get_ocr_model_called = True
    from myocr import OCRSystem

    # 在CPU设备上启用量化优化
    enable_quantization = (device == 'cpu')

    return OCRSystem(
        det_model_path = conf.det_model_path,
        cls_model_path = conf.cls_model_path,
        cls_vertical_model_path = conf.cls_vertical_model_path,
        rec_model_path = conf.rec_model_path,
        rec_char_dict_path = conf.rec_char_dict_path,
        device=device,
        enable_quantization=enable_quantization)

def device_tensor(img: np.ndarray = Depends(read_image),
        device:str = _device_param) -> torch.Tensor:
    # print('device_tensor_start', time.time())
    with torch.inference_mode():
        return torch.as_tensor(img, device=device)[None,:].permute([0,3,1,2]).to(torch.float32)

def text_det(img: torch.Tensor = Depends(device_tensor), 
        model = Depends(get_ocr_model)) -> Tuple[np.ndarray, np.ndarray]:
    # print('text_det_start', time.time())
    with torch.inference_mode():
        return model.text_detect(img)

@app.post('/textdet', tags=[Tags.debug_inspect], summary='图像文本检测',
        response_description='''
    检测结果对象，一般仅使用boxes。

    - **boxes**: 检测框数组，元素为4x2二维数组，记录框的x1y1,x2y2,x3y3,x4y4值
    - **rects**: 检测框数组，元素是长度6的数组，前五个值为框的OpenCV转角矩形描述，最后一个值是检测置信度

    OpenCV转角矩形描述格式：中心点x坐标、中心点y坐标，第一边长、第二边长、第一边与水平方向夹角度数。
    ''')
def api1(text_det: Tuple[np.ndarray, np.ndarray] = Depends(text_det)):
    '''
    检测图像上的文本
    '''
    boxes, rects = text_det
    return { 'boxes': boxes.tolist(), 'rects': rects.tolist() }

def subcut(img: np.ndarray = Depends(read_image)):
    img = cv2.blur(img, (3,3))
    if img.shape[0] > 600 and img.shape[1] > 600:
        img = img[100:-100, 100:-100]
    return img

def fld(img: UploadFile,
        arr: np.ndarray = Depends(subcut)):
    # print('fld_start', time.time())
    if len(arr.shape) == 3:
        arr = cv2.cvtColor(arr, cv2.COLOR_BGR2GRAY)
    lines = None
    if min(arr.shape[:2]) >= 6:
        detector = cv2.ximgproc.createFastLineDetector(length_threshold=200)
        try:
            lines = detector.detect(arr)
        except BaseException as e:
            logger.warning('OpenCV Crash for %s, %s', img.filename, e)
    if lines is None:
        lines = np.array([[]], np.float32)
    return lines.reshape(-1,4)

def rectify_line(lines: np.ndarray = Depends(fld)):
    # print('rectify_line_start', time.time())
    return lines if len(lines) > 3 else np.empty([0,4], np.float32)

def rectify_word(text_det: Tuple[np.ndarray, np.ndarray] = Depends(text_det)):
    # print('rectify_word_start', time.time())
    result, _ = text_det
    if len(result) == 0:
        return np.empty([0,4], np.float32)
    result = result.reshape(-1,8)
    lines = np.concatenate([result[:,:4], result[:,2:6]])
    dh, dw = lines[:,2] - lines[:,0], lines[:,3] - lines[:,1]
    matrix = np.sqrt(dh * dh + dw * dw)
    lines = lines[matrix > 200]
    return lines if len(lines) > 5 else np.empty([0,4], np.float32)

@app.post('/rectify', tags=[Tags.debug_inspect], summary='图像倾斜检测，返回角度值',
        response_description='检出倾斜角度值，浮点数')
def rectify(rectify_line = Depends(rectify_line),
        rectify_word = Depends(rectify_word),
        set_angle: float = Query(1.0, description='容错角度，检出小于此值返回0')) -> float:
    # print('rectify_start', time.time())
    # print('line', rectify_line[:5], len(rectify_line))
    # print('word', rectify_word[:5], len(rectify_word))
    lines = rectify_line if len(rectify_line) > 0 else rectify_word
    if len(lines) == 0:
        return 0
    lines = [[i[2], i[3], i[0], i[1]] if i[0] > i[2] else list(i) for i in lines]
    lines = np.array(lines)
    if len(lines) > 5:
        lh, lw = lines[:, 2] - lines[:, 0], lines[:, 3] - lines[:, 1]
        matrix = np.sqrt(lh*lh + lw*lw)  # 返回的是弧度
        if max(matrix) > 400:
            lines = np.column_stack((lines, np.transpose(matrix)))  # 增加一列类别
            lines = lines[np.lexsort(-lines.T)][:5]  # 按最后一列逆序排序,且只取最长的5条线
    # print('final line', lines[:5], len(lines))
    lh, lw = lines[:, 2] - lines[:, 0], lines[:, 3] - lines[:, 1]
    # 若直线为竖线，则计算正切值的时候，则将lines_h， lines_w，赋值为lines_w， -lines_h
    lh_new = [-lw[i] if (-lw[i] > lh[i]) else lw[i] if (lw[i] > lh[i]) else lh[i] for i in range(len(lh))]
    lw_new = [lh[i] if (-lw[i] > lh[i]) else -lh[i] if (lw[i] > lh[i]) else lw[i] for i in range(len(lh))]
    angles = np.arctan2(lw_new, lh_new)
    l1 = angles>np.pi/4
    l2 = angles<-np.pi/4
    l3 = ~(l1 | l2)
    angles = l1*(np.pi/2-angles)+l2*(angles+np.pi/2)+l3*angles
    angle = float(np.median(angles) * 180 / np.pi)
    # print('angle', angle)
    if np.abs(angle) > 20:
        angle = 0
    # angle, results = self.east_exe.rectify(image, name, True, False, False, [], draw_flag=False, out_flag=True)
    return 0 if abs(angle) < set_angle else round(angle, 2)

@app.post('/house', tags=[Tags.debug_inspect], summary='文本方向，返回角度值',
        response_description='文本方向角度值，整数：0,90,180,270之一')
def house_angle(img: torch.Tensor = Depends(device_tensor),
        text_det: Tuple[np.ndarray, np.ndarray] = Depends(text_det), 
        model = Depends(get_ocr_model)) -> int:
    # print('house_angle_start', time.time())
    boxes, rects = text_det
    sel = (rects[:,2]>=25)&(rects[:,3]>=25)&(np.abs(rects[:,3]-rects[:,2])>=45)
    boxes, rects = boxes[sel][:20], rects[sel][:20]
    if len(boxes) < 1:
        return 0
    with torch.inference_mode():
        patch = model.extract_patches(img, boxes, rects, model.cls_dsize); del img, boxes
        res1, res2 = model._text_vertical(patch), model._text_classifier(patch); del patch
        res1 = res1[1].cpu().numpy(); res2 = res2[1].cpu().numpy()
    res1 = (res1 == 0)
    rects, res2 = rects[res1], res2[res1]
    if len(rects) < 3:
        return 0
    angle = ((rects[:, 4] / 90.).round().astype(np.int64) + res2 * 2) % 4
    logger.info('%r %r %r', angle, angle.dtype, len(angle))
    angle = np.argmax(np.bincount(angle))
    # rects[:,-1] *= 1000; print(rects[:5].round(-1))
    return ((1-int(angle))%4)*90

def _edge_chk_contour(o: np.ndarray, side: int, pt: int, 
        h: int, w: int) -> bool:
    # o: Nx1x2
    m, n = o.min(0), o.max(0) # 1x2
    return np.any([(m<side) & (n<=pt), (m+pt>=[[h,w]]) & (n+side>[[h,w]])])

@app.post('/edge', tags=[Tags.debug_inspect], summary='黑边检测',
        response_description='二维数组，每条黑边所在矩形位置')
def edge_remove(img: np.ndarray = Depends(read_image)) -> int:
    # print('edge_remove_start', time.time())
    outs = _edge_remove(img)
    return [[*o.min(0).tolist()[0],*o.max(0).tolist()[0]] for o in outs]
    # return len(outs)

def _edge_remove(img):
    ret = []
    w, h = img.shape[:2]
    # if min(w, h) < 600:
    #     return ret
    pt, side, c = int(w * h / 20000), 20, 200
    _, b = cv2.threshold(cv2.cvtColor(img, cv2.COLOR_BGR2GRAY), 0, 255, cv2.THRESH_OTSU)
    cv2.line(b, (c,0), (c,c), 255, thickness=4)
    cv2.line(b, (h-c,0), (h-c,c), 255, thickness=4)
    cv2.line(b, (h-c,w-c), (h-c,w), 255, thickness=4)
    cv2.line(b, (c,w-c), (c,w), 255, thickness=4)
    b = cv2.dilate(b, cv2.getStructuringElement(0, (3,3)))
    b[:, [0,-1]], b[[0,-1], :] = 255, 255
    outs = cv2.findContours(b, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)[-2:][0]
    max_con, min_con = int(w * h / 16), int(w * h / 4000)
    outs = [o for o in outs if min_con < cv2.contourArea(o) < max_con and
            _edge_chk_contour(o, side, pt, h, w)]
    # print([cv2.approxPolyDP(o, 0.01*cv2.arcLength(o, 1), 1).tolist() for o in outs])
    return outs

@_cache(Cache(1))
def get_stain_model(conf: Settings = Depends(get_conf),
        device:str = _device_param):
    from model_quantization import quantized_torch_jit_load
    enable_quantization = (device == 'cpu')
    return quantized_torch_jit_load(conf.stain_model_path, map_location=device,
                                  enable_quantization=enable_quantization)

def stain_hole(img: torch.Tensor = Depends(device_tensor),
        model = Depends(get_stain_model),
        ) -> Tuple[list[Any], list[Any]]:
    # print('stain_hole_start', time.time())
    with torch.inference_mode():
        r = model([img[0]])[1][0]
        return { k:v.cpu().numpy() for k, v in r.items() }

_object_detection_response_description_fmtstr = '''物件检测结果对象，下各字段为等长数组。

- **labels**: 描述检出对象类别，{}
- **scores**: 描述检出置信度
- **boxes**:  描述检出各框的位置，各元素为长度4数组，表示框左上角和右下角坐标[x1y1x2y2]
'''

@app.post('/stain', tags=[Tags.debug_inspect], summary='图像污点、装订孔检测',
        response_description=_object_detection_response_description_fmtstr.format(
            '1装订孔，2污点'))
def api2(stain_hole: dict[str, np.ndarray] = Depends(stain_hole)):
    return { k:v.tolist() for k, v in stain_hole.items() }

def stain(stain_hole: dict[str, np.ndarray] = Depends(stain_hole),
        set_stain: int = Query(default=0, ge=0,
            description='容许污点数，在容许内审查返回空数组，否则返回二维数组表示各污点所在矩形位置')):
    # print('stain_start', time.time())
    a = [k.astype(int).tolist() for i,j,k in zip(stain_hole['labels'], stain_hole['scores'],
        stain_hole['boxes']) if i == 2 and j >= 0.5 and (k[2]-k[0]>=4 or k[3]-k[1]>=4)]
    return a if len(a) > set_stain else []
    # return sum((i == 2 and j >= 0.5 and (k[2]-k[0]>=4 or k[3]-k[1]>=4)) for i,j,k in
    #         zip(stain_hole['labels'], stain_hole['scores'],
    #             stain_hole['boxes'])).item() > set_stain

def hole(stain_hole: dict[str, np.ndarray] = Depends(stain_hole),
        set_hole: int = Query(default=0, ge=0,
            description='容许装订孔数，在容许内审查返回空数组，否则返回二维数组表示各装订孔所在矩形位置')):
    # print('hole_start', time.time())
    a = [k.astype(int).tolist() for i,j,k in zip(stain_hole['labels'], stain_hole['scores'],
        stain_hole['boxes']) if i == 1 and j >= 0.5 and (k[2]-k[0]>=4 or k[3]-k[1]>=4)]
    return a if len(a) > set_hole else []
    # return sum((i == 1 and j >= 0.5 and (k[2]-k[0]>=4 or k[3]-k[1]>=4)) for i,j,k in
    #         zip(stain_hole['labels'], stain_hole['scores'],
    #             stain_hole['boxes'])).item() > set_hole

def get_dpi(img: UploadFile) -> Tuple[float,float]:
    with open_image(img) as f:
        if 'jfif_unit' in f.info.keys() and f.info['jfif_unit'] == 2:
            dpi = f.info['jfif_density'][0]/0.3937008
            dpi = dpi, dpi
        else:
            dpi = f.info.get('dpi', (200,200))
        return dpi

@app.post('/dpi', tags=[Tags.debug_inspect], summary='图像dpi检查',
        response_description='数值型，不合格返回数值 合格返回0')
def dpi(get_dpi: Tuple[float,float] = Depends(get_dpi),
        set_dpi: int = Query(300, description='要求的DPI下限')) -> float:
    dpi = round(get_dpi[0], 2)
    return dpi if dpi < set_dpi else 0

_bit_depth_mode_dict = {
    '1': 1, 'L': 8, 'P': 8, 'RGB': 24, 'RGBA': 32, 'CMYK': 32,
    'YCbCr': 24, 'I': 32, 'F': 32}

@app.post('/bitdepth', tags=[Tags.debug_inspect], summary='图像位深度检查',
         response_description='整数型，不合格返回数值 合格返回0')
def bit_depth(img: UploadFile,
        set_bit_depth: int = Query(24, description='要求的位深度下限')) -> int:
    # bit_depth = get_bit_depth(img_name)[0]
    with open_image(img) as f:
        bit_depth = _bit_depth_mode_dict[f.mode]
        return bit_depth if bit_depth < set_bit_depth else 0

def format_(img: UploadFile, set_format:list[str] = Query(
    default=['.jpg', '.jpeg', '.png', '.tif', '.tiff'],
    description='容许图像文件名后缀')) -> bool:
    _, suffix = os.path.splitext(img.filename)
    return suffix.lower() not in set_format

def kb(img: UploadFile,
    set_kb:int = Query(default=500, gt=0,
        description='容许图像大小下限(KB)'),
    max_kb: int = Query(default=1000, gt=0,
        description='容许图像大小上限(KB)')):
    fileno = img.file.fileno()
    size = os.stat(fileno).st_size // 1024
    return size if size < set_kb or size > max_kb else 0

_AX = np.array([5.85,6.93,8.27,11.69,16.53,23.38])
_AY = np.array([8.27,9.84,11.69,16.53,23.38,33.06])
_AN = ['A5','B5','A4','A3','A2','A1','A0']

@app.post('/pagesize', tags=[Tags.debug_inspect], summary='图像纸张大小检测',
        response_description='字符串，页面纸张规格名称（A5,B5,A4,A3,A2,A1,A0）')
def page_size(img: UploadFile,
        get_dpi: Tuple[int,int] = Depends(get_dpi)) -> str:
    # page_size = detect_a4_a3(img.file)
    with open_image(img) as f:
        h, w = f.size
    h, w = (w, h) if h > w else (h, w)
    AX, AY = _AX * get_dpi[0], _AY * get_dpi[1]
    a = h * w
    if (h <= AX[0] and w <= AY[0]) or a < (AX[0]*AY[0]+AX[1]*AY[1])/2:
        pz = 0
    elif (h <= AX[1] and w <= AY[1]) or a < (AX[1]*AY[1]+AX[2]*AY[2])/2:
        pz = 1
    elif (h <= AX[2] and w <= AY[2]) or a < AX[2]*AY[2]*1.5:
        pass
    elif (h <= AX[2] and w <= AY[2]) or a < AX[2]*AY[2]*1.5:
        pz = 2
    elif  (h <= AX[3] and w <= AY[3]) or a < AX[3]*AY[3]*1.5:
        pz = 3
    elif  (h <= AX[4] and w <= AY[4]) or a < AX[4]*AY[4]*1.5:
        pz = 4
    elif  (h <= AX[5] and w <= AY[5]) or a < AX[5]*AY[5]*1.5:
        pz = 5
    else:
        pz = 6
    return _AN[pz]

_JSTD_LUNA_QTBL = np.array([
   16, 11, 10, 16, 24, 40, 51, 61, 12, 12, 14, 19, 26, 58, 60, 55,
   14, 13, 16, 24, 40, 57, 69, 56, 14, 17, 22, 29, 51, 87, 80, 62,
   18, 22, 37, 56, 68,109,103, 77, 24, 35, 55, 64, 81,104,113, 92,
   49, 64, 78, 87,103,121,120,101, 72, 92, 95, 98,112,100,103, 99])

def _jp_lq2q(x):
   if x == 1:
       y = 1
   elif x == 100:
       y = 50
   elif x > 100:
       y = math.ceil(5000/x)
   else:
       y = 100 - math.ceil(x/2)
   return y
_jp_lq2q = np.frompyfunc(_jp_lq2q, 1, 1)

@app.post('/jpeg/quality', tags=[Tags.debug_inspect], summary='JPEG质量审查',
         response_description='整数型，不合格返回数值 合格返回0')
def _jp_quality(img: UploadFile,
        set_quality: int = Query(90, ge=0, le=100, description='要求的JPEG质量下限')) -> int:
    with open_image(img) as f:
        if f.format != 'JPEG':
            return 0
        q = getattr(f, 'quantization', None)
    if not q and not 0 in q:
        return 0
    q = np.array(q[0])
    mask = (0 < q) & (q < 32767)
    q = np.ceil((q * 100 - 50)/_JSTD_LUNA_QTBL)
    q = _jp_lq2q(q)
    q = int((q*mask).sum()/mask.sum()+0.5)
    return q if q < set_quality else 0

@app.post('/blank', tags=[Tags.debug_inspect], summary='图像空白检查',
        response_description='布尔值，true 不合格 false 合格')
def find_blank(img: np.ndarray = Depends(read_image),
        set_sensitivity: int = Query(3, description='检测敏感度参数')) -> bool:
    # print('find_blank_start', time.time())
    h, w = img.shape[:2]
    if min(img.shape[:2]) >= 12:
        img = cv2.resize(img, [w//12, h//12], cv2.INTER_LINEAR)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        _, img = cv2.threshold(img, 250, 255, cv2.THRESH_TOZERO_INV)
        s = img.sum().item()
        # print('find_blank_end', time.time())
        return s < set_sensitivity
    return False

@app.post('/pair/dup', tags=[Tags.inspect], summary='图像重复检查',
        response_description='文件名到布尔型字典，targets列表中每张图像是否和img重复')
def find_duplicate(img: UploadFile, targets: list[UploadFile] = [], 
        set_sensitivity: int = Query(3, description='检测敏感度参数')) -> dict[str, bool]:
    '''
    检查targets和img重复的图像，上传文件需指定文件名(filename)

    - **img**: 上传图像文件
    - **targets**: 上传多个待查图像文件，打不开的文件视作不重复
    '''
    with open_image(img) as f:
        size, mode = f.size, f.mode
    valids = []
    for target in targets:
        a = False
        try:
            with open_image(target) as f:
                a = (size == f.size) and (mode == f.mode)
        except HTTPException:
            pass
        valids.append(a)
    if min(size) >= 12:
        def imread(x):
            x.seek(0); x = cv2.imdecode(np.fromfile(x, dtype=np.uint8), cv2.IMREAD_REDUCED_COLOR_4)
            if x is None: return None
            return cv2.resize(x, [x.shape[1]//3,x.shape[0]//3], cv2.INTER_LINEAR)
    else:
        def imread(x):
            x.seek(0); return cv2.imdecode(np.fromfile(x, dtype=np.uint8), cv2.IMREAD_COLOR)
    filename = img.filename; img = imread(img.file)
    if img is None:
        logger.warning('OpenCV imread failed for %s size %r', filename, size)
        return {target.filename:False for target in targets}
    results = {}
    for target, valid in zip(targets, valids):
        filename = target.filename
        if valid:
            valid = False; target = imread(target.file)
            if target is not None:
                valid = cv2.absdiff(img, target).sum().item() < set_sensitivity
        results[filename] = valid
    return results

class BarcodePoint(BaseModel):
    class Config:
        orm_mode = True
    x: int
    y: int

class BarcodePosition(BaseModel):
    class Config:
        orm_mode = True
    bottom_left: BarcodePoint
    bottom_right: BarcodePoint
    top_left: BarcodePoint
    top_right: BarcodePoint

class BarcodeResult(BaseModel):
    class Config:
        orm_mode = True
    content_type: str = Field(description='符号内容类型')
    format: str = Field(description='符号条码格式，Code39|Code128|EAN-13|PDF417|QRCode等')
    orientation: int
    position: BarcodePosition
    symbology_identifier: str = Field(description='符号标识符，读卡器扫码枪等为数据消息添加的标准字符串前缀')
    text: str = Field(description='识别到的文本信息')
    valid: bool

    @validator('content_type','format', pre=True)
    def cppenum_to_str(cls, v):
        return v.name

@app.post('/scan/barcode', tags=[Tags.debug_inspect], summary='检测识别图像上的二维码',
        response_description='条码、二维码列表', response_model=list[BarcodeResult])
def scan_barcode(img: UploadFile):
    with open_image(img) as img:
        results = read_barcodes(img)
    return results

@app.delete('/alot/{ident}', tags=[Tags.inspect], summary='删除一个单张图像审查接口',
        response_description='''
        成功删除。
        暂存/openapi.json响应结果的请重新请求，在/docs页面工作请刷新浏览。
        ''', responses={
            '404': { 'description': '找不到指定接口' }
        })
async def alot_delete(ident: str):
    '''
    - **ident**: 接口名称
    '''
    found = None
    for i in reversed(app.routes):
        if i.path == f'/alot/{ident}/inspect':
            found = i
            break
    if found:
        logger.info('found existing %s %r', found.path, found)
        app.routes.remove(found)
        app.openapi_schema = None
        return 'ok'
    else:
        raise HTTPException(status_code=404, detail={ 'ident': ident,
            'error': 'not found' })

_alot_qdesc_fmtstr = '开启%s，响应追加%s字段'

@app.post('/alot/{ident}', tags=[Tags.inspect], summary='创建一个单张图像审查接口',
        response_description='''
        创建审查接口 POST /alot/{ident}/inspect 。
        暂存/openapi.json响应结果的请重新请求，在/docs页面工作请刷新浏览。
        如接口已经存在，该操作会替换。如什么审查都不做，不创建接口。
        返回字符串为生成的接口代码，可供调试参考。
        ''')
async def alot(ident: str,
        flag_rectify: bool = Query(True, description=_alot_qdesc_fmtstr%('倾斜审查', 'rectify')),
        flag_house_angle: bool = Query(True, description=_alot_qdesc_fmtstr%('文本方向审查', 'house_angle')),
        flag_edge_remove: bool = Query(True, description=_alot_qdesc_fmtstr%('黑边审查', 'edge_remove')),
        flag_stain: bool = Query(True, description=_alot_qdesc_fmtstr%('污点审查', 'stain')),
        flag_hole: bool = Query(True, description=_alot_qdesc_fmtstr%('装订孔审查', 'hole')),
        flag_dpi: bool = Query(True, description=_alot_qdesc_fmtstr%('DPI审查', 'dpi')),
        flag_bit_depth: bool = Query(True, description=_alot_qdesc_fmtstr%('位深度审查', 'bit_depth')),
        flag_format: bool = Query(True, description=_alot_qdesc_fmtstr%('文件格式审查', 'format')),
        flag_kb: bool = Query(True, description=_alot_qdesc_fmtstr%('KB值审查', 'kb')),
        flag_page_size: bool = Query(True, description=_alot_qdesc_fmtstr%('纸张大小检测', 'page_size')),
        flag_quality: bool = Query(True, description=_alot_qdesc_fmtstr%('JPEG质量检测', 'quality')),
        flag_blank: bool = Query(True, description=_alot_qdesc_fmtstr%('空白审查', 'blank')),
        ) -> str:
    found = None
    for i in reversed(app.routes):
        if i.path == f'/alot/{ident}/inspect':
            found = i
            break
    if found:
        logger.info('found existing %s %r', found.path, found)
        app.routes.remove(found)
    text = '''
@app.post('/alot/{ident}/inspect', tags=[Tags.inspect], summary='名为 {ident} 的用户定义单张图像审查接口')
def alot_{ident}_inspect({depends}):
    ret = dict()
    {assigns}
    return ret
'''
    depends, assigns = '', ''
    if flag_rectify:
        depends += 'rectify = Depends(rectify),'
        assigns += 'ret["rectify"] = rectify;'
    if flag_house_angle:
        depends += 'house_angle = Depends(house_angle),'
        assigns += 'ret["house_angle"] = house_angle;'
    if flag_edge_remove:
        depends += 'edge_remove = Depends(edge_remove),'
        assigns += 'ret["edge_remove"] = edge_remove;'
    if flag_stain:
        depends += 'stain = Depends(stain),'
        assigns += 'ret["stain"] = stain;'
    if flag_hole:
        depends += 'hole = Depends(hole),'
        assigns += 'ret["hole"] = hole;'
    if flag_dpi:
        depends += 'dpi = Depends(dpi),'
        assigns += 'ret["dpi"] = dpi;'
    if flag_bit_depth:
        depends += 'bit_depth = Depends(bit_depth),'
        assigns += 'ret["bit_depth"] = bit_depth;'
    if flag_format:
        depends += 'format_ = Depends(format_),'
        assigns += 'ret["format"] = format_;'
    if flag_kb:
        depends += 'kb = Depends(kb),'
        assigns += 'ret["kb"] = kb;'
    if flag_page_size:
        depends += 'page_size = Depends(page_size),'
        assigns += 'ret["page_size"] = page_size;'
    if flag_quality:
        depends += 'quality = Depends(_jp_quality),'
        assigns += 'ret["quality"] = quality;'
    if flag_blank:
        depends += 'find_blank = Depends(find_blank),'
        assigns += 'ret["blank"] = find_blank;'
    source = text.format(ident=ident, depends=depends, assigns=assigns)
    if depends:
        exec(source)
        app.openapi_schema = None
    return source

@_cache(Cache(1))
def get_layout_model(conf: Settings = Depends(get_conf),
        device:str = _device_param):
    from model_quantization import quantized_torch_jit_load
    enable_quantization = (device == 'cpu')
    return quantized_torch_jit_load(conf.layout_model_path, map_location=device,
                                  enable_quantization=enable_quantization)

def layout_pred(img: torch.Tensor = Depends(device_tensor), 
        model = Depends(get_layout_model), device: str = _device_param):
    with torch.inference_mode():
        r = model([img[0]])[1][0]
        return { k:v.cpu().numpy() for k, v in r.items() }

@app.post('/layout_pred', tags=[Tags.debug_process], summary='图像处理中版面分析原始结果',
         response_description=_object_detection_response_description_fmtstr.format(
         '类别编号和文字对照如有需要请联系研发'))
def layout_pred_api(layout_pred: dict[str, np.ndarray] = Depends(layout_pred)):
    return { k:v.tolist() for k, v in layout_pred.items() }

def param_house_records_cut(house_records_cut: str = Query( # 下左上右
    default='0,0,0,0', regex=r'^\d+,\d+,\d+,\d+$',
    description='固定像素值裁剪配置，下左上右顺序')):
    return [int(i) for i in house_records_cut.split(',')]

@_cache(Cache(1))
def get_kp_model(conf: Settings = Depends(get_conf),
        device: str = _device_param):
    from model_quantization import quantized_torch_jit_load
    enable_quantization = (device == 'cpu')
    return quantized_torch_jit_load(conf.kp_model_path, map_location=device,
                                  enable_quantization=enable_quantization)

@_cache(Cache(1))
def get_pe_model(conf: Settings = Depends(get_conf),
        device: str = _device_param):
    from model_quantization import quantized_torch_jit_load
    enable_quantization = (device == 'cpu')
    return quantized_torch_jit_load(conf.pe_model_path, map_location=device,
                                  enable_quantization=enable_quantization)

def param_post_cut(post_cut: str = Query( # 下左上右
    default='0,0,0,0', regex=r'^\d+,\d+,\d+,\d+$',
    description='自动裁剪量调整配置，可在自动裁剪边界基础上多裁，下左上右顺序')):
    # TODO support cut less (requires changes to the call of perspective)
    return [int(i) for i in post_cut.split(',')]

def cut_white(img: np.ndarray = Depends(read_image), 
        house_records_cut: list[int] = Depends(param_house_records_cut),
        flag_cut: bool = Query(False, description='开启自动裁剪(KP)'),
        flag_cut_pe: bool = Query(False, description='开启自动裁剪(PE)'),
        post_cut: list[int] = Depends(param_post_cut),
        flag_white_edge: bool = Query(False, description='开启去白边'),
        conf: Settings = Depends(get_conf), device:str = _device_param):
    # img = batch_cut(img)
    down, left, up, right = house_records_cut; h, w = img.shape[:2]
    img = img[up:h-down, left:w-right]
    if img.size == 0:
        raise HTTPException(status_code=304)
    # img = custom_cut(img)
    if flag_cut_pe:
        img = _pe_gwu_cut(img, conf, device)
    if flag_cut:
        # resize_img, kps_result, valid, ratio_x, ratio_y = self.keypoint_predict_exe.kp_predict(image)
        # image = self.keypoint_predict_exe.extract(image, kps_result)
        kps_result = _kp_predict(img, conf, device)
        img = _extract(img, kps_result)
    if flag_cut or flag_cut_pe:
        down, left, up, right = post_cut; h, w = img.shape[:2]
        if up < h-down and left < w-right:
            img = img[up:h-down, left:w-right]
    # img = cut_white(img)
    if flag_white_edge:
        # self.cut_white_edge(image, volumes, name, self.flag_white_edge)
        img = _cut_white_edge(img)
    return img

def _kp_predict(img, conf, device):
    tgtlen,outlen = 481,128
    resized = cv2.resize(img, [tgtlen,tgtlen], interpolation=cv2.INTER_LINEAR)
    ratio_x, ratio_y = img.shape[1] / tgtlen, img.shape[0] / tgtlen
    model = get_kp_model(conf=conf, device=device)
    with torch.inference_mode():
        heatmap = model(device_tensor(resized, device))[0].cpu().numpy()
    num_kps = heatmap.shape[0]; kps_result = np.zeros([num_kps, 3])
    for j in range(num_kps):
        hm_j = heatmap[j]#; print('hm_j:', (hm_j/ 255.)[-((j+1)//2)%2*8][-((j)//2)%2*8])
        idx = hm_j.argmax()
        y, x = np.unravel_index(idx, hm_j.shape)
        px, py = int(math.floor(x + 0.5)), int(math.floor(y + 0.5))
        if 1 < px < outlen - 1 and 1 < py < outlen - 1:
            diff = np.sign([hm_j[py][px + 1] - hm_j[py][px - 1],
                hm_j[py + 1][px] - hm_j[py - 1][px]])
            x += diff[0] * .25; y += diff[1] * .25
        kps_result[j, :2] = (x * tgtlen / outlen, y * tgtlen / outlen)
        kps_result[j, 2] = hm_j.max() / 255
    kps_result[:,:2] *= [[ratio_x, ratio_y]]
    return kps_result

def _extract(img, kps_result):
    pred_points = kps_result[:,:2]
    pred_points = _order_points_new(pred_points)
    rect = cv2.minAreaRect(pred_points)
    box_pred = np.int0(cv2.boxPoints(rect))
    if _check_points(pred_points, box_pred):
        imgout = _rotate(img, box_pred[0], box_pred[1], box_pred[2], box_pred[3])
    else:
        imgout = img
    return imgout

def _order_points_new(pts):
    # sort the points based on their x-coordinates
    xSorted = pts[np.argsort(pts[:, 0]), :]
    # grab the left-most and right-most points from the sorted
    # x-roodinate points
    leftMost = xSorted[:2, :]
    rightMost = xSorted[2:, :]
    if leftMost[0, 1] != leftMost[1, 1]:
        leftMost = leftMost[np.argsort(leftMost[:, 1]), :]
    else:
        leftMost = leftMost[np.argsort(leftMost[:, 0])[::-1], :]
    (tl, bl) = leftMost
    if rightMost[0, 1] != rightMost[1, 1]:
        rightMost = rightMost[np.argsort(rightMost[:, 1]), :]
    else:
        rightMost = rightMost[np.argsort(rightMost[:, 0])[::-1], :]
    (tr, br) = rightMost
    return np.array([tl, tr, br, bl], dtype="float32")

def _check_points(p, box_pred):
    a = _get_angle(p[0], p[1], p[2])
    b = _get_angle(p[1], p[2], p[3])
    c = _get_angle(p[3], p[0], p[1])
    d = _get_angle(p[2], p[3], p[0])
    if 70<a<110 and 70<b<110 and 70<c<110 and 70<d<110:
        aa = _get_length(box_pred[0], box_pred[1])
        bb = _get_length(box_pred[1], box_pred[2])
        if min(aa,bb) > 0 and max(aa,bb) / min(aa,bb) < 3:
            return True
    return False

def _get_length(p0, p1):
    return math.sqrt((pow((p0[0]-p1[0]),2)) + (pow((p0[1]-p1[1]),2)))

def _get_angle(p1, p2, p3):
    a = math.sqrt((p2[0] - p3[0]) * (p2[0] - p3[0]) + (p2[1] - p3[1]) * (p2[1] - p3[1]))
    b = math.sqrt((p1[0] - p3[0]) * (p1[0] - p3[0]) + (p1[1] - p3[1]) * (p1[1] - p3[1]))
    c = math.sqrt((p1[0] - p2[0]) * (p1[0] - p2[0]) + (p1[1] - p2[1]) * (p1[1] - p2[1]))
    if a*c == 0:
         return 0
    else:
        cosin = (b * b - a * a - c * c) / (-2 * a * c)
        if abs(cosin) >= 1:
            return 180
        else:
            division = math.acos(cosin)
    if abs(division) > 3.14:
        return 180
    else:
        return math.degrees(division)

def _rotate(img, pt1, pt2, pt3, pt4):
    pt1, pt2, pt3, pt4 = _order_points_new(np.array([pt1, pt2, pt3, pt4]))
    w = int(round(np.linalg.norm([pt2[0]-pt1[0], pt2[1]-pt1[1]])))
    h = int(round(np.linalg.norm([pt3[0]-pt2[0], pt3[1]-pt2[1]])))
    point1 = np.array([pt1, pt2, pt3, pt4], dtype="float32")
    point2 = np.array([[0, 0], [w, 0], [w, h], [0, h]], dtype="float32")
    M = cv2.getAffineTransform(point1[:3], point2[:3])
    imgout = cv2.warpAffine(img, M, (w, h), borderValue=(255, 255, 255))
    return imgout

def _pe_gwu_cut(x: np.ndarray, conf, device):
    with torch.inference_mode():
        model = get_pe_model(conf=conf, device=device)
        isz = 384; hlf = isz//2
        h,w = x.shape[:2]
        h1,w1 = isz-h%isz, isz-w%isz
        h1,h2,w1,w2 = h1//2,h1-h1//2,w1//2,w1-w1//2
        xb = np.pad(x, [[h1,h2],[w1,w2],[0,0]])
        hb,wb = xb.shape[:2]
        xb = cv2.resize(xb, [isz,isz], interpolation=cv2.INTER_NEAREST)
        sx,sy = wb//isz,hb//isz
        xb = cv2.cvtColor(xb, cv2.COLOR_BGR2RGB)
        xb = torch.as_tensor(xb, device=device)[None].permute(0,3,1,2)
        out = model(xb).cpu().numpy()[0,0]; del xb
        out[:h1//sy+1,:] = 0
        out[isz-h2//sy-1:,:] = 0
        out[:,:w1//sx+1] = 0
        out[:,isz-w2//sx-1:] = 0
        out = np.pad(out, [[hlf,hlf],[hlf,hlf]])
        out = cv2.morphologyEx(out, cv2.MORPH_OPEN,
            cv2.getStructuringElement(cv2.MORPH_RECT, (9, 9)), iterations=5)
        out = cv2.erode(out, cv2.getStructuringElement(cv2.MORPH_RECT, (7, 7)))
        out = cv2.Canny(out, 225, 255)
        out = cv2.dilate(out, cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5)))
        corners = cv2.findContours(out, cv2.RETR_LIST, cv2.CHAIN_APPROX_NONE, offset=[-hlf,-hlf])[-2]
        if len(corners) == 0:
            return x
        corners = sorted(corners, key=cv2.contourArea, reverse=True)[0]
        corners = np.concatenate(corners)
        corners = corners * [[sx,sy]] - [[w1,h1]]
        rect = cv2.minAreaRect(corners.reshape(-1,1,2))
        corners = np.int32(cv2.boxPoints(rect))
        # print(corners, w1,wb-w2,h1,hb-h2)
        out = _rotate(x, corners[0], corners[1], corners[2], corners[3])
        return out

def _cut_white_edge(img):
    # if not check_shape(img):
    #     return img
    if min(img.shape[:2]) < 6:
        return img
    im = cv2.resize(img, [img.shape[1]//6, img.shape[0]//6])
    im_hsv = cv2.cvtColor(im, cv2.COLOR_BGR2HSV)
    thresh = _find_color(im_hsv, color=_White); del im_hsv
    cv2.bitwise_not(thresh, thresh)
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3,3))
    thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
    thresh = cv2.erode(thresh, kernel)
    contours = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[-2]
    area = [cv2.contourArea(cnt) for cnt in contours]
    if (len(area) == 0) or ((len(area) > 0) and (np.max(area) < im.shape[0] * im.shape[1] // 3000)):
        return img
    max_idx = np.argmax(area); del thresh
    # thresh[:] = 0; cv2.fillConvexPoly(thresh, contours[max_idx], 255)
    rect = cv2.minAreaRect(contours[max_idx])
    if rect == []:
        return img
    else:
        box_pred = _order_points_new(np.int0(cv2.boxPoints(rect)))
        for x in range(0,4):
            if box_pred[x][0] < 0 or box_pred[x][1] < 0:
                box_pred = None; break
        if box_pred is not None:
            left_point_x = np.min(box_pred[:, 0]); right_point_x = np.max(box_pred[:, 0])
            top_point_y = np.min(box_pred[:, 1]); bottom_point_y = np.max(box_pred[:, 1])
            factor = 0.92
            if(abs(bottom_point_y-top_point_y) > im.shape[0]*factor and 
                    abs(right_point_x-left_point_x) > im.shape[1]*factor):
                pt1, pt2, pt3, pt4 = box_pred * 6
                imgout = _rotate(img, pt1, pt2, pt3, pt4)
                return imgout
    return img

_White = [([0, 0, 141], [25, 20, 255])]

def _find_color(im, color):
    for (lower, upper) in color:
        lower = np.array(lower, dtype="uint8")
        upper = np.array(upper, dtype="uint8")
    mask = cv2.inRange(im, lower, upper)
    ret, thresh = cv2.threshold(mask, 127, 255, cv2.THRESH_BINARY)
    return thresh

# @app.post('/cut_white')
# def cut_white_testapi(img: np.ndarray = Depends(cut_white)):
#     return kps_result.tolist()

def layout_house(img: np.ndarray = Depends(cut_white),
        get_dpi: Tuple[int,int] = Depends(get_dpi),
        model = Depends(get_layout_model), device: str = _device_param):
    # 印章、边框、肖像
    # rotate, cut, left, right = check_cut(image, get_dpi(dic['img_name'])[0], threshold=600)
    dpi = get_dpi[0]
    cut, left, right = _check_cut(img, dpi, threshold=600)
    if not cut:
        # layout_dic = layout_pred(image)
        layout = layout_pred(device_tensor(img, device), model, device)
    else:
        addnum = float(left.shape[0])
        left  = layout_pred(device_tensor(left , device), model, device)
        right = layout_pred(device_tensor(right, device), model, device)
        layout = _layout_add(left, right, addnum)
    # [boxes, labels, scores] [Nx4, N, N]
    layout['stamp'] = _det_pred_number(layout, 3)
    layout['frame'] = _det_pred_number(layout, 18)
    layout['face'] = _det_pred_number(layout, 14)
    return layout

def _check_cut(img, dpi, threshold=600):
    cut, left, right = False, None, None
    if False: # TODO os.environ.get('CHECK_CUT', 'False'):
        if dpi == 300 and max(img.shape) > 3300 and img.shape[0] > img.shape[1]:
            left  = img[:img.shape[0]//2,:]
            right = img[img.shape[0]//2:,:]
            cut = True
    return cut, left, right

def _layout_add(left, right, addnum):
    add = np.array([0, addnum, 0, addnum], dtype=np.float32)
    return {k:np.concatenate([left[k],right[k]+add if k == 'boxes' else right[k]])
            for k in left.keys()}

def _det_pred_number(det, number, intensity=0.5):
    return det['boxes'][(det['scores']>=intensity)*(det['labels']==number)]

@app.post('/proc_layout_house', tags=[Tags.debug_process], summary='图像处理中检出印章、边框、肖像',
         response_description='''
         以x1y1x2y2的数组形式响应印章、边框、肖像三种版面元素。响应字典里包含：
         - **stamp**: 印章
         - **frame**: 边框
         - **face**:  肖像
         三字段，其余字段归属版面分析原始结果或其他处理后结果。
         ''')
def layout_house_api(layout_house: dict[str, np.ndarray] = Depends(layout_house)):
    return { k:v.tolist() for k, v in layout_house.items() }

# def get_crop_name 基于配置根据文件名不做纠偏、去阴影
# def get_name 基于配置根据文件名不做黑边、去污、去阴影
def vars_from_svc(request: Request,
    img: np.ndarray = Depends(cut_white),
    svc_code: str = Query('', description='逗号分隔的客户服务合同号')):
    ret = dict()
    for code in svc_code.split(','):
        if code == 'KB-XS-2023-0067':
            ret['flag_border_white'] = True
            ret['flag_rotate_keep_size'] = True
            ret['param_border_filter'] = 1
            img = cv2.resize(img, (400,400), interpolation=cv2.INTER_NEAREST)
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV+cv2.THRESH_OTSU)
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5,5))
            eroded = cv2.erode(thresh, kernel)
            dilated = cv2.dilate(eroded, kernel)
            white = 400*400*255
            sub1 = cv2.subtract(img[:,:,1], img[:,:,0])
            sub2 = cv2.subtract(img[:,:,2], img[:,:,1])
            tag = dilated.sum()/white > 0.01 and ((sub1 > 80)|(sub2 > 80)).sum() < 1280
            if tag:
                ret['flag_rectify'] = False
                ret['black_thick'] = 0
                ret['black_thin_auto'] = 0
    return ret

@app.post('/proc_cal_angle', tags=[Tags.debug_process], summary='图像处理中计算纠偏角度',
         response_description='''
         纠偏角度，浮点数
         ''')
def houghline_detect_cal_angle(img: UploadFile,
        array: np.ndarray = Depends(cut_white),
        conf: Settings = Depends(get_conf),
        device: str = _device_param,
        flag_rectify : bool = Query(False, description='开启纠偏'),
        exvars = Depends(vars_from_svc)):
    flag_rectify = exvars.get('flag_rectify', flag_rectify)
    if not flag_rectify:
        return 0
    _line = rectify_line(fld(img, _subcut_up(array)))
    if not len(_line):
        _line = rectify_line(fld(img, _subcut_down(array)))
    if not len(_line):
        model = get_ocr_model(conf=conf, device=device) # cost 0.5 seconds absolutely a bug
        _word = rectify_word(text_det(device_tensor(array, device), model))
    else:
        _word = np.empty([0,4], np.float32)
    return rectify(_line, _word, set_angle=0.0)

def _subcut_up(img: np.ndarray):
    img = cv2.blur(img, (3,3))
    if img.shape[0] > 600 and img.shape[1] > 600:
        img = img[100:img.shape[0]//2,100:-100]
    else:
        img = img # TODO
    return img

def _subcut_down(img: np.ndarray):
    img = cv2.blur(img, (3,3))
    if img.shape[0] > 600 and img.shape[1] > 600:
        img = img[img.shape[0]//2:-100,100:-50]
    else:
        img = img # TODO
    return img

def predict_stain(img: np.ndarray = Depends(cut_white),
        flag_house_detectstain: bool = Query(False, description='''开启去污

        在智慧文档平台图像处理生产部署期间，
        有根据图像文件名称判断，选择开启该选项。
        '''),
        conf: Settings = Depends(get_conf), device: str = _device_param):
    d = ['hole', 'stain', 'class3']
    if flag_house_detectstain:
        model = get_stain_model(conf=conf, device=device)
        r = stain_hole(device_tensor(img, device), model)
        return { d[i]:_det_pred_number(r, i+1) for i in range(3) }
    else:
        return { k:np.zeros([0,4], np.float32) for k in d }

@app.post('/proc_stain', tags=[Tags.debug_process], summary='图像处理计算装订孔污点及class3',
         response_description='''
         响应字典各字段为x1y1x2y2的数组。开启去污时据实检测，否则各字段响应空数组。
         - **hole**: 装订孔
         - **stain**: 污点
         - **class3**: 污点检测中检出的小页码
         ''')
def proc_api2(predict_stain: dict[str, np.ndarray] = Depends(predict_stain)):
    return { k:v.tolist() for k, v in predict_stain.items() }

# @_cache(Cache(1))
# def get_protect_model(conf:Settings = Depends(get_conf),
#         device:str = _device_param):
#     return torch.jit.load(conf.protect_model_path, map_location=device)
# 
# def protect_pred(img: torch.Tensor = Depends(device_tensor), 
#         model = Depends(get_protect_model), device: str = _device_param):
#     with torch.inference_mode():
#         r = model([img[0]])[1][0]
#         return { k:v.cpu().numpy() for k, v in r.items() }

_include_classes = {
    'IDCARD_LICENSES': ['005'],
    'IDS_LICENSES': ['016', '046', '060', '069', '079', '149',
                     '155', '170', '192', '209', '213', '218',
                     '227', '327', '472', '523', '527', '603',
                     '622', '625'],
    }
_include_classes = { w:k for k, v in _include_classes.items() for w in v }

@_cache(Cache(1))
def get_v69_model(conf: Settings = Depends(get_conf),
        device:str = _device_param):
    from model_quantization import quantized_torch_jit_load
    enable_quantization = (device == 'cpu')
    return quantized_torch_jit_load(conf.v69_model_path, map_location=device,
                                  enable_quantization=enable_quantization)

@app.post('/proc_v69', tags=[Tags.debug_process], summary='图像处理中推断含身份证、证照的页面',
    response_description='''
    响应一字符串，有三种可能，含义如下
    - IDCARD_LICENSES : 身份证
    - IDS_LICENSES : 证照
    - OTHER_LICENSES : 其他
    ''')
def predict_in_class(img: np.ndarray = Depends(cut_white),
        layout_house = Depends(layout_house),
        conf: Settings = Depends(get_conf),
        device:str = _device_param):
    img = cv2.resize(img, [192,192])
    tensor = device_tensor(img, device) / 255.
    model = get_v69_model(conf=conf, device=device)
    with torch.inference_mode():
        result = torch.softmax(model(tensor), -1)[0].cpu()
        result = torch.max(result, -1)
        prob, label = result
        prob, label = prob.item(), str(label.item()).zfill(3)
    ret = 'OTHER_LICENSES'
    if prob > 0.9:
        ret = _include_classes.get(label, 'OTHER_LICENSES')
        if ret == 'IDCARD_LICENSES' and len(layout_house['face']) == 0:
            ret = 'OTHER_LICENSES'
    # certificate_flag = self.Predict_exe.predict_image_in_classes(image, include_classes)
    # if certificate_flag == 'IDCARD_LICENSES':
    #     if (len(dic_la['face'])) == 0: # 没有查到肖像的身份证是误检。
    #         certificate_flag = 'OTHER_LICENSES'
    return ret

@app.post('/proc_color_f', tags=[Tags.debug_process], summary='图像处理中进行颜色纸张检测',
         response_description='''
         响应一个布尔值。开启颜色纸张保真选项时据实检测，否则总是响应假。真表示有颜色纸张。
         ''')
def color_f(img: np.ndarray = Depends(cut_white),
        flag_color_keep_real : bool = Query(False, description='开启颜色纸张保真选项'),
        layout_house = Depends(layout_house)):
    # 如果有边框线，用框线以内的范围计算背景色。
    if flag_color_keep_real:
        return _classify_color_paper(img, layout_house['frame'])
    #   color_flag = self.classify_color_paper(image, dic_la['frame'])
    return False

def _classify_color_paper(img: np.ndarray, frame: np.ndarray):
    color_cnt = 0
    total = 0
    if img.size < 10:
        return False
    if len(frame) > 0:
        for i in frame:
            crop = img[int(i[1]):int(i[3]),int(i[0]):int(i[2])]
            bk = _get_dominant_color(crop)[::-1]
            color_cnt += _iscolor(bk); total += 1
            if color_cnt > 2:
                return True
    # keep it as is, known to work
    cw = img.shape[0]//30
    ch = img.shape[1]//25
    for i in range(10):
        crop = _random_crop(img, [cw,ch], padding=None)
        bk = _get_dominant_color(crop)[::-1]
        color_cnt += _iscolor(bk); total += 1
        if color_cnt > 2:
            return True
        if total > 9:
            break
    return False

_random_crop_padding_param_warned = False

def _random_crop(img, shape, padding=None):
    h,w = img.shape[:2]
    ch,cw = shape
    if padding and not _random_crop_padding_param_warned:
        logger.warning('padding param in _random_crop is obsolete')
        _random_crop_padding_param_warned = True
    nh, nw = np.random.randint([h-ch, w-cw])
    return img[nh:nh+ch, nw:nw+cw]

def _get_dominant_color(img):
    num = 3
    data = np.float32(img.reshape([-1,3]))
    crit = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 3, 1.0)
    flags = cv2.KMEANS_RANDOM_CENTERS
    compactness, labels, centers = cv2.kmeans(data, num, None, crit, 1, flags)
    t = np.bincount(labels.ravel())
    color = np.median(data[labels.ravel() == t.argmax()], axis=0)
    return [int(color[0]), int(color[1]), int(color[2])]

def _iscolor(bk):
    h, s, v = cv2.cvtColor(np.array([[bk]], np.uint8), cv2.COLOR_RGB2HSV).flat
    return int(s > 16)

def get_intensity(protect_small_object_intensity: int = 
        Query(1, ge=1, le=10, description='小目标保护强度')) -> float:
    return 0.5 - 0.05*(protect_small_object_intensity - 1)

def predict_photo(layout = Depends(layout_house),
        flag_protect_photo: bool = Query(True, description='照片保护'),
        flag_protect_p: bool = Query(True, description='小目标保护，该项在生产中没有部署关闭'),
        intensity:float = Depends(get_intensity)):
    layout['photo'] = np.concatenate([ # photo and face
        _det_pred_number(layout, 4, intensity),
        _det_pred_number(layout, 14, intensity),
        ]) if flag_protect_photo else np.zeros([0,4], np.float32)
    # 保护p和小页码 #### 
    layout['class10'] = np.concatenate([
        _det_pred_number(layout, 8, intensity), _det_pred_number(layout, 19, intensity),
        _det_pred_number(layout, 5)]) if flag_protect_p else np.zeros([0,4], np.float32)
    return layout

predict_p = predict_photo

def param_blackline_scope(blackline_scope: str = Query( # 上下左右
    default='20,20,20,20', regex=r'^\d+,\d+,\d+,\d+$',
    description='去黑边选项，细黑边所在边缘范围，上下左右顺序')):
    return [int(i) for i in blackline_scope.split(',')]

def cut_black(img: np.ndarray = Depends(cut_white),
        flag_black_edge: bool = Query(False, description='''开启去黑边

        在智慧文档平台图像处理生产部署期间，
        有根据图像文件名称判断，选择开启该选项。
        '''),
        blackline_scope: list[int] = Depends(param_blackline_scope),
        black_thick: int = Query(0, description='去黑边选项，去除粗黑边'),
        black_thin: int = Query(1, description='去黑边选项，去除细黑边'),
        flag_color_keep_real: bool = False,
        predict_in_class: str = Depends(predict_in_class),
        color_f: bool = Depends(color_f),
        layout = Depends(layout_house),
        exvars = Depends(vars_from_svc)):
    flag_border_white = exvars.get('flag_border_white', False)
    black_thick = exvars.get('black_thick', black_thick)
    black_thin_auto = exvars.get('black_thin_auto', 1)
    param_border_filter = exvars.get('param_border_filter', 0)
    color = flag_color_keep_real and (color_f or predict_in_class == 'IDS_LICENSES')
    if flag_black_edge and not color:
        if black_thin and black_thin_auto:
            thin = _det_pred_number(layout, 23)
        else:
            thin = np.zeros([0,4], np.float32)
        img = _black_rm_new(img, black_thick, black_thin, layout['stamp'], thin, layout,
                            blackline_scope, flag_border_white, param_border_filter)
    return img

def _black_rm_new(img, black_thick, black_thin, stamp, thin, layout, blackline_scope,
                  flag_border_white=False, param_border_filter=0):
    if black_thick or black_thin:
        ori = img.copy(order='K')
    if black_thick:
        img = _black_edge_rm(img, stamp, flag_border_white)
    if black_thin:
        h, w = img.shape[:2]
        b = [255,255,255] if flag_border_white else _get_dominant_color(cv2.resize(img, [20,20]))
        for s in thin:
            s0, s1, s2, s3 = s
            if (s0 < 10 and s2 < 250) or (s1 < 10 and s3 < 300) or (s2 > w - 10 and s0 > w - 250) or (
                    s3 > h - 10 and s1 > h - 200):
                cv2.rectangle(img, [int(s0),int(s1)], [int(s2),int(s3)], [b[0],b[1],b[2]], -1)
        img = _black_line_rm(img, b, blackline_scope)
    if black_thick or black_thin:
        # TODO copy and paste back is slow
        for box in [b for b,l,s in zip(layout['boxes'], layout['labels'], layout['scores'])
            if s > 0.5 and l not in [1, 12, 13, 18, 23, 21]]:
            x1, y1, x2, y2 = np.int0(box)
            img[y1:y2, x1:x2] = ori[y1:y2, x1:x2]
        if param_border_filter:
            r = int(np.median(blackline_scope) * 2.0);
            r = r if r > 0 else 40
            img = cv2.ximgproc.createGuidedFilter(ori, r, 5).filter(img)
            if r >= 10:
                ori = img.copy('K');
                r = r // 5;
                b = r // 2
                img[:b] = 255;
                img[-b:] = 255;
                img[:, :b] = 255;
                img[:, -b:] = 255;
                img[:r] = cv2.ximgproc.createGuidedFilter(ori[:r], r, 5).filter(img[:r])
                img[-r:] = cv2.ximgproc.createGuidedFilter(ori[-r:], r, 5).filter(
                    img[-r:])
                img[:, :r] = cv2.ximgproc.createGuidedFilter(ori[:, :r], r, 5).filter(
                    img[:, :r])
                img[:, -r:] = cv2.ximgproc.createGuidedFilter(ori[:, -r:], r, 5).filter(
                    img[:, -r:])
    return img

def _black_edge_rm(img, stamp, flag_border_white=False):
    outs = _edge_remove(img)
    if len(outs) > 0:
        mask = np.zeros_like(img[:,:,:1])
        mask = cv2.fillPoly(mask, outs, color=[255,255,255])
        mask = ~cv2.dilate(mask, cv2.getStructuringElement(0, (3,3)), iterations=4)
        b = [255,255,255] if flag_border_white else _get_dominant_color(cv2.resize(img, [20,20]))
        replace = np.array([[[b[0], b[1], b[2]]]], np.uint8)
        result = np.where(np.expand_dims(mask,-1), img, replace)
        mask_stamp = np.zeros_like(img[:,:,:1])
        for i in stamp:
            i0,i1,i2,i3 = np.int0(i)
            mask_stamp[i1:i3, i0:i2] = 255
        img = np.where(mask_stamp, img, result)
    return img

def _black_line_rm(img, b, blackline_scope):
    h,w = img.shape[:2]
    b = [b[0],b[1],b[2]]
    c0, c1, c2, c3 = blackline_scope
    img = cv2.rectangle(img, [0,0], [c2,h], b, -1)
    img = cv2.rectangle(img, [w-c3,0], [w,h], b, -1)
    img = cv2.rectangle(img, [0,h-c1], [w,h], b, -1)
    img = cv2.rectangle(img, [0,0], [w,c0], b, -1)
    return img

def shadow_remove_all(img: np.ndarray = Depends(cut_black),
        flag_shadow: bool = Query(False, description='''开启去阴影
        
        在智慧文档平台图像处理生产部署期间，
        有根据图像文件名称判断，选择开启该选项、选择不同亮度、对比度参数的设计。
        '''),
        flag_color_keep_real: bool = False,
        predict_in_class = Depends(predict_in_class),
        color_f = Depends(color_f),
        layout = Depends(layout_house),
        param_brightness: float = Query(1.2, ge=1.0, le=2.0, description='亮度参数，参见flag_shadow'),
        param_contrast: float = Query(1.3, ge=1.0, le=2.0, description='对比度参数，参见flag_shadow'),
        param_brightness_idcard: float = Query(1.15, ge=1.0, le=2.0,
            description='''身份证亮度参数，对检出的身份证有效，参见flag_shadow'''),
        oem_deepen_light_word: int = Query(0, description='项目客户A定制选项'),
        oem_deepen_gamma: int = Query(2, description='项目客户A定制参数'),
        device: str = _device_param,
        ):
    # 去阴影
    if flag_shadow:
        color = flag_color_keep_real and (color_f or predict_in_class == 'IDS_LICENSES')
        if oem_deepen_light_word and not color:
            deepen = layout['stamp']; show_b = _det_pred_number(layout, 16) # 表单
            s0,s1 = img.shape[:2]
            for i0,i1,i2,i3 in show_b:
                if i3 - i1 < i2 - i0:
                    r = (i3-i1)*(i2-i0)/(s0*s1)
                    if 0.12 < r < 0.3:
                        deepen = np.concatenate([deepen,
                            np.array([[s1/6,s0/5,s1/6*5,s0/5*4]],np.float32)])
                        break
        else:
            deepen = np.zeros([0,4],np.float32)
        idcard = predict_in_class == 'IDCARD_LICENSES'
        param_brightness = param_brightness_idcard if idcard else param_brightness
        # NOTE CPU is slow like 0.15 seconds each (plus final ascontiguous 0.06)
        # GPU runs instantly (remember data transfer in uint8 cost 0.01 otherwise 0.06)
        not_run = True
        with torch.inference_mode():
            if param_contrast > 1.0 and not color and not idcard:
                img = torch.as_tensor(img)[None].permute([0,3,1,2]).to(device).float()[0]/ 255.
                img = VTF.adjust_contrast(img, param_contrast)
            if param_brightness > 1.0 and not color:
                if not isinstance(img, torch.Tensor):
                    img = torch.as_tensor(img)[None].permute([0,3,1,2]).to(device).float()[0] / 255.
                img = VTF.adjust_brightness(img, param_brightness)
            if isinstance(img, torch.Tensor):
                img = VTF.convert_image_dtype(img, torch.uint8).permute([1,2,0]).cpu().numpy()
                not_run = False
                assert img.data.contiguous
        if len(deepen) > 0:
            g = _gamma_trans(img, gamma=oem_deepen_gamma)
            g = np.clip(g*1.1, 0, 255).astype(np.uint8)
            if not_run:
                img = img.copy(order='K')
            for x1,y1,x2,y2 in np.int0(deepen):
                img[y1:y2,x1:x2] = g[y1:y2,x1:x2]
    return img

def _gamma_trans(img, gamma):
    table = np.array([((i / 255.0) ** gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
    return cv2.LUT(img, table)

def replace_stain(img: np.ndarray = Depends(shadow_remove_all),
        predict_stain = Depends(predict_stain)):
    # 去污
    if min(img.shape[:2]) >= 21 and len(predict_stain['hole']) + len(predict_stain['stain']) > 0:
        img = img.copy(order='K')
        h,w = img.shape[:2]
        max_len = max(144, int(h*w/28000))
        img_pad = cv2.copyMakeBorder(img[20:h-20,20:w-20], 20, 100, 20, 100, cv2.BORDER_REFLECT)
        for i in itertools.chain(predict_stain['hole'], predict_stain['stain']):
            i0,i1,i2,i3 = np.int0(i)
            if i2-i0 <= max_len and i3-i1 <= max_len:
                if i1 >= i3 or i2 >= img_pad.shape[1] - 20:
                    # print("Invalid slice indices!------------------------>>>>>>>>>",i1,i3,i2,img_pad.shape[1])
                    continue
                color = _get_dominant_color(img_pad[i1:i3,i2:i2+20])
                cv2.rectangle(img, [i0,i1], [i2,i3], [color[0],color[1],color[2]], -1)
    return img

def param_protect_area(protect_area: list[str] = Query( # x1y1x2y2
        default=['0'], regex=r'^[01]$|^\d+,\d+,\d+,\d+$',
        description='''版心保护配置，一个可选的新功能，鲜有生产部署

        - 0 不启用
        - 1 自动
        - 逗号分隔四个数字 x1y1x2y2形式保护区域左上和右下坐标''')):
        return [[int(i) for i in p.split(',')] for p in protect_area]

def recover_p(img: np.ndarray = Depends(replace_stain),
        img_ori: np.ndarray = Depends(shadow_remove_all),
        layout = Depends(predict_p), predict_stain = Depends(predict_stain),
        param_protect_areas: list[list[int]] = Depends(param_protect_area)):
    # 恢复页码和p # 版心保护
    boxes = []
    if [1] in param_protect_areas:
        boxes = [b for b,l,s in zip(layout['boxes'], layout['labels'], layout['scores'])
            if s > 0.5 and l not in [12, 13, 23]]
    elif len(layout['class10']) > 0:
        boxes = [b for b in layout['class10']]
    boxes.extend(predict_stain['class3'])
    for param_protect_area in param_protect_areas:
        if len(param_protect_area) == 4:
            boxes.append(np.array(param_protect_area, np.float32))
    for box in boxes:
        x1, y1, x2, y2 = np.int0(box)
        img[y1:y2, x1:x2] = img_ori[y1:y2, x1:x2]
    return img

def recover_stamp_table_all(img: np.ndarray = Depends(recover_p),
        img_ori: np.ndarray = Depends(shadow_remove_all),
        layout = Depends(predict_p), 
        oem_deepen_light_word: int = 0):
    # 恢复印章 表格
    if not oem_deepen_light_word: # 
        boxes = layout['stamp']
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3,3))
        for box in boxes:
            x1,y1,x2,y2 = np.int0(box)
            crop = img_ori[y1:y2,x1:x2]
            mask = _stamp_mask(crop)
            mask = cv2.dilate(mask, kernel, iterations=4)
            img[y1:y2,x1:x2] = np.where(np.expand_dims(mask,-1), crop, img[y1:y2,x1:x2])
    return img

def _stamp_mask(img):
    ret = np.zeros_like(img[:,:,0])
    b = cv2.cvtColor(img, cv2.COLOR_BGR2YUV)
    thresh, mask = cv2.threshold(b[:,:,2], 0, 255, cv2.THRESH_BINARY+cv2.THRESH_OTSU)
    if thresh > 132:
        if mask.sum() / (mask.size*255) < 0.45:
            ret = mask
        # bgmask = b[:,:,2] > thresh
        twice = np.where(mask, 128, b[:,:,2])
        thresh, mask2 = cv2.threshold(twice, 0, 255, cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        if thresh > 128:
            temp = cv2.add(ret, mask2)
            if temp.sum() / (temp.size*255) < 0.45:
                ret = temp
    return ret

def protect_photo_house(img: np.ndarray = Depends(recover_stamp_table_all),
        img_ori: np.ndarray = Depends(cut_black),
        predict_photo = Depends(predict_photo)):
    # 恢复照片、肖像
    for box in predict_photo['photo']:
        x1,y1,x2,y2 = np.int0(box)
        img[y1:y2,x1:x2] = img_ori[y1:y2,x1:x2]
    return img

def rotate_small(img: np.ndarray = Depends(protect_photo_house),
        flag_rotate_border_white: bool = Query(True, description='纠偏时用白色补齐边缘'),
        houghline_detect_cal_angle = Depends(houghline_detect_cal_angle),
        exvars = Depends(vars_from_svc)):
    flag_rotate_keep_size = exvars.get('flag_rotate_keep_size', False)
    flag_border_white = exvars.get('flag_border_white', flag_rotate_border_white)
    return _rotate_degree(img, houghline_detect_cal_angle, flag_border_white, flag_rotate_keep_size)

def _rotate_degree(img, angle, flag_border_white=True, flag_rotate_keep_size=False):
    if angle == 0:
        return img
    elif angle in [90,180,270]:
        return np.rot90(img, -angle//90)
    else:
        im = img if flag_rotate_keep_size else img[3:-3,3:-3]
        h,w = im.shape[:2]
        bk = [255,255,255]
        if not flag_border_white:
            tmp = _get_pages_outside(im)
            if tmp.size >= 10:
                bk = _get_dominant_color(tmp)
        rad = math.radians(angle)
        sin, cos = math.fabs(math.sin(rad)), math.fabs(math.cos(rad))
        hN = int(w*sin + h*cos)
        wN = int(h*sin + w*cos)
        if hN == h or wN == w:
            return img
        mat = cv2.getRotationMatrix2D([w/2, h/2], angle, 1)
        if flag_rotate_keep_size:
            wN, hN = w, h
        else:
            mat[0,2] += (wN - w) / 2; mat[1,2] += (hN - h) / 2
        img = cv2.warpAffine(im, mat, [wN, hN], borderValue=bk)
        return img

def _get_pages_outside(img):
    img = img[20:-20,20:-20]
    h,w = img.shape[:2]
    mh, mw = 20, 80
    wl, wr = (0,w) if w < mw else (w//2-mw//2, w//2+mw//2)
    hl, hr = (0,h) if h < mw else (h//2-mw//2, h//2+mw//2)
    h0 = h if h < mh else mh
    w0 = w if w < mh else mh
    tmp = np.stack([
        np.rot90(img[:h0, wl:wr]),
        img[hl:hr, :w0],
        np.rot90(img[-h0:, wl:wr]),
        img[hl:hr, -w0:]
        ], axis=-2)
    return tmp

def rotate_big(img: np.ndarray = Depends(rotate_small),
        flag_house_angle: bool = Query(False, description='开启文本方向纠正'),
        conf: Settings = Depends(get_conf),
        device:str = _device_param):
    if flag_house_angle:
        model = get_ocr_model(conf=conf, device=device)
        tensor = device_tensor(img, device)
        det = text_det(tensor, model)
        angle = house_angle(tensor, det, model)
        img = _rotate_degree(img, angle)
    return img

_sharp_kernel = np.array(
        [[-1,-1,-1,-1,-1],
         [-1, 2, 2, 2,-1],
         [-1, 2, 8, 2,-1],
         [-1, 2, 2, 2,-1],
         [-1,-1,-1,-1,-1]], np.float32) / 8.0

def param_makeup_edge(makeup_edge: Optional[str] = Query( # 下左上右
    default=None, regex=r'^\d+,\d+,\d+,\d+$',
    description='固定像素值补边配置，下左上右顺序')):
    return [int(i) for i in makeup_edge.split(',')] if makeup_edge else []

def dotted_sharp_make_a4(img: np.ndarray = Depends(rotate_big),
        dotted_line: str = Query('', include_in_schema=False, regex='^$'),
        flag_sharp: bool = Query(True, description='''开启锐化

        在智慧文档平台图像处理生产部署期间，
        有根据图像文件名称判断，选择开启该选项。
        '''),
        flag_makeup_A: bool = Query(False, description='开启补图像到A4或补配置值'),
        flag_color_keep_real: bool = False,
        predict_in_class: str = Depends(predict_in_class),
        color_f: bool = Depends(color_f),
        get_dpi: Tuple[int,int] = Depends(get_dpi),
        makeup_edge: list[int] = Depends(param_makeup_edge),
        ):
    # img = dotted(img)
    color = flag_color_keep_real and (color_f or predict_in_class == 'IDS_LICENSES')
    # img = custom_sharp(img)
    if flag_sharp and not color:
        img = cv2.filter2D(img, -1, _sharp_kernel)
    # img = makeup_a4(img)
    if flag_makeup_A and makeup_edge:
        down, left, up, right = makeup_edge
        img = cv2.copyMakeBorder(img, up, down, left, right, cv2.BORDER_CONSTANT, value=[255, 255, 255])
    elif flag_makeup_A:
        d0, d1 = get_dpi
        s0, s1 = img.shape[:2]
        if s0 < s1:
            a0, a1 = int(_AX[2]*d0), int(_AY[2]*d1)
        else:
            a0, a1 = int(_AY[2]*d0), int(_AX[2]*d1)
        if s0 < a0 and s1 < a1:
            left, up = (a0 - s0)//2, (a1 - s1)//2
            img = cv2.copyMakeBorder(img, left, left, up, up, cv2.BORDER_CONSTANT, value=[255, 255, 255])
    return img

def xxx_save(img: UploadFile,
        dpi: Tuple[int,int] = Depends(get_dpi),
        result: np.ndarray = Depends(dotted_sharp_make_a4),
        set_compress: Optional[int] = Query(None,
        description='图像压缩率，留空表示不设压缩',ge=1,le=100)
        ) -> StreamingResponse:
    ifd = ImageFileDirectory_v2()
    with open_image(img) as f:
        bit_depth = _bit_depth_mode_dict[f.mode]
        nchan = len(f.getbands())
        fmt = f.format or 'JPEG'
        if hasattr(f, 'tag_v2'):
            ifd.update(f.tag_v2)
            del ifd[322], ifd[323], ifd[324], ifd[325], ifd[256], ifd[257], ifd[278], ifd[279]
            ifd[274] = 1 # Orientation
    result = Image.fromarray(cv2.cvtColor(result, cv2.COLOR_BGR2RGB))
    _, ext = os.path.splitext(img.filename); ext = ext.lower()
    kwargs = { 'format': fmt }
    if ifd and nchan == 1 and bit_depth == 1 and fmt == 'TIFF':
        result = result.convert('1')
        kwargs['compression'] = 'group4'
        kwargs['tiffinfo'] = ifd
    elif ifd and nchan == 1 and fmt == 'TIFF':
        result = result.convert('L')
        kwargs['compression'] = 'jpeg'
        kwargs['tiffinfo'] = ifd
    else:
        kwargs['dpi'] = dpi
        if set_compress:
            kwargs['quality'] = set_compress
        if fmt == 'TIFF':
            kwargs['compression'] = 'jpeg'
            kwargs['tiffinfo'] = ifd
    media_type = 'application/octet-stream'
    if fmt in ['JPEG','TIFF','PNG','GIF']:
        media_type = 'image/{}'.format(fmt.lower())
    b = BytesIO()
    result.save(b, **kwargs)
    length = b.tell()
    return StreamingResponse(iter([b.getvalue()]), headers={
            'content-length': str(length)
        }, media_type=media_type)

@app.post('/process', tags=[Tags.process], summary='图像处理入口',
        response_description='''
        响应处理后的图片，含以下响应头设置：
        - content-length: 响应长度
        - content-type: 响应媒体类型，根据输入，会响应以下值之一
                image/jpeg,image/tiff,image/png,image/gif,application/octet-stream
        ''',
        responses={
            304: { 'description': '图像处理经过判断，对 **img** 不做处理' }
        },
        )
def process(xxx_save = Depends(xxx_save)) -> StreamingResponse:
    '''
    图像处理接口

    在默认值基础上，通常档案图像处理工序会：
    - 开启文本方向纠正flag_house_angle、开启纠偏flag_rectify、
    开启去黑边flag_black_edge、开启去阴影flag_shadow、开启去污flag_house_detectstain
    - 设置固定像素值裁剪house_records_cut、设置压缩率set_compress
    - 调整亮度参数param_brightness、对比度参数param_contrast

    在智慧文档平台图像处理生产部署期间，有根据图像文件名称判断，基于配置实现，
    选择性关闭去黑边、关闭去污、关闭去阴影、和/或关闭锐化等。
    '''
    return xxx_save

def _cadre_table_contours(img: np.ndarray = Depends(read_image)) -> np.ndarray:
    im = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    im = cv2.GaussianBlur(im, (3,3), 0, im)
    im = cv2.bitwise_not(im, im)
    im = cv2.adaptiveThreshold(im, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, -2, im)
    scale = 20
    h, w = im.shape
    lines = [cv2.morphologyEx(im, cv2.MORPH_OPEN,
        cv2.getStructuringElement(cv2.MORPH_RECT, k))
        for k in [(1, h//scale), (w//scale, 1)]]
    return cv2.add(*lines, im)

@app.post('/cadre/table_contours', tags=[Tags.debug_process])
def cadre_table_contours(im: np.ndarray = Depends(_cadre_table_contours)) -> StreamingResponse:
    im = Image.fromarray(im)
    media_type = 'image/png'
    b = BytesIO()
    im.save(b, format='png')
    length = b.tell()
    return StreamingResponse(iter([b.getvalue()]), headers={
        'content-length': str(length)
        }, media_type=media_type)

@_cache(Cache(1))
def get_det0789_model(conf: Settings = Depends(get_conf),
        device:str = _device_param):
    from model_quantization import quantized_torch_jit_load
    enable_quantization = (device == 'cpu')
    return quantized_torch_jit_load(conf.det0789_model_path, map_location=device,
                                  enable_quantization=enable_quantization)

def predict_class8(img: np.ndarray = Depends(cut_white),
    layout = Depends(predict_photo),
    flag_protect_photo: bool = Query(True, description='照片保护'),
    conf: Settings = Depends(get_conf), device: str = _device_param):
    if flag_protect_photo:
        model = get_det0789_model(conf=conf, device=device)
        r = stain_hole(device_tensor(img, device), model)
        layout['class8'] = _det_pred_number(r, 8)
    else:
        layout['class8'] = np.zeros([0,4],np.float32)
    return layout

def param_stamp_yuv(stamp_yuv: str = Query( # 下左上右
    default='98,96,249', regex=r'^\d+,\d+,\d+$',
    description='鲜红章颜色值，YUV值')):
    return [int(i) for i in stamp_yuv.split(',')]

def _cadre_shadow(img: np.ndarray = Depends(cut_white),
    layout: dict[str, np.ndarray] = Depends(predict_class8),
    flag_shadow: bool = Query(True, description='开启去阴影'),
    param_brightness: float = Query(1.4, ge=1.0, le=2.0, description='亮度参数，参见flag_shadow'),
    param_remove_dot: int = Query(3, ge=2, le=4, description='去除小噪点参数'),
    stamp_yuv: list[int] = Depends(param_stamp_yuv),
    conf: Settings = Depends(get_conf),
    device:str = _device_param):
    if not (flag_shadow and len(layout['class8']) == 0):
        return img
    model = get_ocr_model(conf=conf, device=device)
    text_box, _ = text_det(device_tensor(img, device), model); del model, _
    im = img; del img
    stamp = layout['stamp']; del layout
    remove_dot = param_remove_dot; del param_remove_dot
    a = im.copy()
    ori_brightness = _cadre_get_lightness(a)
    b = cv2.cvtColor(a, cv2.COLOR_BGR2YUV)
    l = np.clip(1 - ((b[:, :, 1] / 1. - 128) ** 2 + (b[:, :, 2] / 1. - 128) ** 2) ** (1 / 2) / 181, 0.0,
        1.0)  # more color, less adjustment
    b[:, :, 0] = np.clip((l * (b[:, :, 0] / 255.) ** 4 * 255. + (1 - l) * b[:, :, 0]) *
        (ori_brightness < 213 and 2.65 or 2.2), 0,
        255)  # gamma 4, light 2.2
    thresh, mask = cv2.threshold(b[:, :, 0], 0, 255, cv2.THRESH_BINARY_INV+cv2.THRESH_OTSU)
    from skimage.filters import threshold_sauvola
    thresh_sauvola = threshold_sauvola(im[ :, :, 1], window_size=25, k=0.1)
    binary_sauvola = im[ :, :, 1] < thresh_sauvola
    mask = np.where(binary_sauvola, mask, np.uint8(0))
    img_linear = np.zeros([im.shape[0], im.shape[1]], np.uint8)
    for i in stamp:
        i = np.int32(i)
        cv2.rectangle(img_linear, i[:2], i[2:], 255, -1)
    # img_linear = cv2.dilate(img_linear, (3, 3))
    thresh, mask_stamp = cv2.threshold(b[:, :, 2], 0, 255, cv2.THRESH_BINARY+cv2.THRESH_OTSU)
    if thresh < 141:  # 由于黑章和蓝章会分割出粉红色的一篇，设置若红色阈值分割值小于141，则不保护章
        mask_stamp = np.zeros([im.shape[0], im.shape[1]], np.uint8)
    mask_stamp = cv2.bitwise_and(img_linear, img_linear, mask=mask_stamp)
    mask_stamp = ~mask_stamp
    assert (len(a.shape) == 3)  # 3D arrays
    if ori_brightness < 213:
        brightness = 2.2
    elif ori_brightness > 240 and ori_brightness < 253:
        # y = -0.0007x**2 + 0.3334x - 37.866
        # 252 250 245 240 241 239
        # 1.22 1.25 1.35 1.39 1.382 1.4
        brightness = -0.0007*(ori_brightness**2) + 0.3334*ori_brightness - 37.866
    # elif ori_brightness > 238 and ori_brightness < 253:
        # brightness = brightness - (ori_brightness-238)/15.0*(brightness-1)
    elif ori_brightness >= 253:
        brightness = 1.0
    else:
        brightness = param_brightness
    a = np.clip(a * brightness, 0, 255).astype(np.uint8)
    a = cv2.cvtColor(a, cv2.COLOR_BGR2YUV)
    c = a.copy()
    c[:, :, 0] = np.where(mask, b[:, :, 0], a[:, :, 0])
    # c = cv2.seamlessClone(a, b, mask, (a.shape[1]//2, a.shape[0]//2), cv2.MIXED_CLONE)
    # _,d = cv2.threshold(cv2.cvtColor(b,cv2.COLOR_BGR2GRAY), 0,255,cv2.THRESH_BINARY+cv2.THRESH_OTSU)
    # c = np.where(np.dstack([mask,mask,mask]), np.dstack([b,b,b]), a)
    # redref = np.array([[[142, 91, 227]]], np.uint8)  # BGR 66,100,255
    redref = np.array([[stamp_yuv]], np.uint8)  # BGR:0,20,220 YUV:98,96,249
    cm = np.ma.array(c, mask=np.dstack([mask_stamp, mask_stamp, mask_stamp]))
    # cm = (cm / 2 + redref / 2).astype(np.uint8)
    cm = (cm / 1000 + redref / 1000 * 999).astype(np.uint8)
    cm = cv2.cvtColor(cm, cv2.COLOR_YUV2BGR)
    ########################新增文本二值化区域加入#########################################
    mask_word = np.zeros([im.shape[0], im.shape[1]], np.uint8)
    for pts in text_box:
        cv2.fillPoly(mask_word, pts=np.int32([pts]), color=255)
    # 对文本框内部做二值化
    gray = cv2.cvtColor(cm, cv2.COLOR_BGR2GRAY)  # 把输入图像灰度化
    # 对输入的单通道矩阵逐像素进行阈值分割。
    ret, bina = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV+cv2.THRESH_OTSU)
    bina = bina & mask_word
    maskin_1 = cv2.bitwise_and(cm, cm, mask=bina)
    ########################新增文本二值化区域加入#########################################
    ## 1.去除小联通区域
    ## 先把所有表格线抹掉，不然图形内是闭合矩形，无法去小连通区域
    if min(im.shape[:2]) >= 6:
        mask_table = _cadre_table_contours(im)
        img_word = _cadre_rm_smallarea(cm, mask_table, remove_dot)
    else:
        mask_table = np.zeros_like(mask_word)
        img_word = cm
    ## 2.之后加入曝光之后的红色区域：主要是指纹
    maskin = cv2.bitwise_and(cm, cm, mask=~mask_stamp)
    maskout = cv2.bitwise_and(img_word, img_word, mask=mask_stamp)
    img_word = cv2.add(maskin, maskout)
    ## 3.去除小联通区域后，加入曝光之后的文字
    maskout = cv2.bitwise_and(img_word, img_word, mask=~bina)
    img_word = cv2.add(maskin_1, maskout)
    ## 4.之后加入原图的表格线
    maskin = cv2.bitwise_and(im, im, mask=mask_table)
    maskout = cv2.bitwise_and(img_word, img_word, mask=~mask_table)
    img_word = cv2.add(maskin, maskout)
    return img_word

@app.post('/cadre/shadow', tags=[Tags.debug_process])
def cadre_shadow(img: np.ndarray = Depends(_cadre_shadow)) -> StreamingResponse:
    img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    media_type = 'image/jpeg'
    b = BytesIO()
    img.save(b, format='jpeg', quality=95)
    length = b.tell()
    return StreamingResponse(iter([b.getvalue()]), headers={
        'content-length': str(length)
        }, media_type=media_type)

def _cadre_get_lightness(a):
    a = cv2.cvtColor(a, cv2.COLOR_BGR2HSV)
    a = a[:,:,2].mean()
    return a

def _cadre_rm_smallarea(im, mask_table, remove_dot):
    gray = cv2.cvtColor(im, cv2.COLOR_BGR2GRAY)
    binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 25, 10)
    kernel = np.ones((3, 3), np.uint8)  # 给图像闭运算定义核
    mask_table = cv2.dilate(mask_table, kernel, iterations=2)
    binary = ~binary - mask_table
    contours = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)[-2:][0] # APPROX_SIMPLE
    mask = np.zeros([im.shape[0], im.shape[1]], np.uint8)
    for i in range(len(contours)):
        area = cv2.contourArea(contours[i])
        if area <= remove_dot*remove_dot:
            (x, y), = contours[i].ptp(axis=0)+1
            r = x / y
            if r >= 4 or r <= 1/4:
                continue
            cv2.drawContours(mask, [contours[i]], 0, (255, 255, 255), -1)
    mask = cv2.dilate(mask, kernel, iterations=2)
    light = np.ones([im.shape[0], im.shape[1], 3], np.uint8)*255
    inside = cv2.bitwise_and(im, im, mask=~mask)
    outside = cv2.bitwise_and(light, light, mask=mask)
    img = cv2.add(inside, outside)
    return img

def _cadre_predict_stain(img: np.ndarray = Depends(_cadre_shadow),
        flag_house_detectstain: bool = Query(True, description='开启去污'),
        conf: Settings = Depends(get_conf), device: str = _device_param):
    return predict_stain(img, flag_house_detectstain, conf, device)

def _cadre_replace_stain(img: np.ndarray = Depends(_cadre_shadow),
        predict_stain = Depends(_cadre_predict_stain)):
    return replace_stain(img, predict_stain)

def _cadre_recover_p(img: np.ndarray = Depends(_cadre_replace_stain),
        img_ori: np.ndarray = Depends(_cadre_shadow),
        layout = Depends(predict_class8), predict_stain = Depends(_cadre_predict_stain),
        param_protect_areas: list[list[int]] = Depends(param_protect_area)):
    return recover_p(img, img_ori, layout, predict_stain, param_protect_areas)

def _cadre_clip(x):
    return 1 - np.clip(np.abs(x / 64 - 2.), 0., 1.)

def param_replace_background(replace_background: str = Query( # 下左上右
    default='254,246,197', regex=r'^\d+,\d+,\d+$',
    description='替换白色背景功能新背景颜色值，RGB值')):
    return [int(i) for i in replace_background.split(',')]

def _cadre_replace_white(img: np.ndarray = Depends(_cadre_replace_stain),
    layout = Depends(predict_class8),
    flag_background_yellow:bool = Query(True, description='替换白色背景为黄色'),
    background: list[int] = Depends(param_replace_background)):
    if not (flag_background_yellow and len(layout['class8']) == 0):
        return img
    k0 = np.array([[background]], np.uint8)
    k = cv2.cvtColor(k0, cv2.COLOR_RGB2LAB).astype(np.float32)
    k[:, :, 1:] -= 128
    a = img
    b = cv2.cvtColor(a, cv2.COLOR_BGR2LAB)
    l1 = np.clip((b[:,:,0].astype(np.float32)-160)/95, 0., 1.)
    l = (l1 * _cadre_clip(b[:, :, 1]) * _cadre_clip(b[:, :, 2])) ** (1 / 3)
    d = (l * k[0, 0, 0] + (1 - l) * b[:, :, 0]).astype(np.uint8)
    e = l1*_cadre_clip(b[:, :, 1]) * (k[0, 0, 1]) + b[:, :, 1] - 128
    f = l1*_cadre_clip(b[:, :, 2]) * (k[0, 0, 2]) + b[:, :, 2] - 128
    # g = np.sqrt(e*e+f*f)
    # g1 = np.clip(g, 0, 128)
    # g1 = np.where(g == 0, g1, g1 * (np.reciprocal(g)))
    # e, f = e * g1, f * g1
    e = np.clip(e + 128, 0, 255).astype(np.uint8)
    f = np.clip(f + 128, 0, 255).astype(np.uint8)
    c = cv2.cvtColor(np.dstack([d, e, f]), cv2.COLOR_LAB2BGR)
    return c

@app.post('/cadre/replace_white', tags=[Tags.debug_process])
def cadre_replace_white(img: np.ndarray = Depends(_cadre_replace_white)) -> StreamingResponse:
    img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    media_type = 'image/jpeg'
    b = BytesIO()
    img.save(b, format='jpeg', quality=95)
    length = b.tell()
    return StreamingResponse(iter([b.getvalue()]), headers={
        'content-length': str(length)
        }, media_type=media_type)

def _cadre_protect_photo(img: np.ndarray = Depends(_cadre_replace_white),
        img_ori: np.ndarray = Depends(cut_white),
        predict_photo = Depends(predict_class8)):
    return protect_photo_house(img, img_ori, predict_photo)

def _cadre_rotate_small(
        houghline_detect_cal_angle = Depends(houghline_detect_cal_angle),
        img: np.ndarray = Depends(_cadre_protect_photo)):
    return _rotate_degree(img, houghline_detect_cal_angle, False)

def _cadre_xxx_save(img: UploadFile,
        dpi: Tuple[int,int] = Depends(get_dpi),
        result: np.ndarray = Depends(_cadre_rotate_small),
        ) -> StreamingResponse:
    return xxx_save(img, dpi, result, 95)

@app.post('/cadre_process', tags=[Tags.process], summary='干部人事档案图像处理入口',
        response_description='''
        响应处理后的图片，含以下响应头设置：
        - content-length: 响应长度
        - content-type: 响应媒体类型，根据输入，会响应以下值之一
                image/jpeg,image/tiff,image/png,image/gif,application/octet-stream
        ''',
        responses={
            304: { 'description': '图像处理经过判断，对 **img** 不做处理' }
        },
        )
def cadre_process(xxx_save = Depends(_cadre_xxx_save)) -> StreamingResponse:
    '''
    干部人事档案优化图像处理接口 输入应为干部人事档案数字化所描述的原始图像数据。
    功能可在一定程度上减少优化图像的生产时间。

    - 默认开启替换背景为黄色flag_background_yellow、去阴影flag_shadow、
    去污flag_house_detectstain、保护照片flag_protect_photo、小目标保护flag_protect_p
    - 如有变通需求调节去除小噪点参数param_remove_dot、修改印章颜色stamp_yuv、
    修改背景颜色replace_background
    - 其他因内部逻辑复用继承的参数可视情选用，用于其他用途
    '''
    return xxx_save
    # meta, image
    # meta, layout, image
    # meta, layout, angle, image
    # meta, layout, angle, stamp, image
    # meta, layout, angle, stamp, shadow_remove_highres, image
    # meta, layout, angle, stamp, p, shadow_remove_highres, image
    # meta, layout, angle, stamp, stain, p, shadow_remove_highres, image
    # meta, layout, angle, stamp, stain, replace_stain_a, p, shadow_remove_highres, image
    # meta, layout, angle, stamp, replace_stain_b, image
    # meta, layout, angle, stamp, replace_white, image
    # meta, layout, angle, stamp, protect_photo
    # meta, rotate_degree
    # xxx_save

def ocr_param_fx(fx: int = Query(default=1, ge=1, le=2, description='''文本行宽倍率

    该参数为文字识别步骤的配置参数，整数 1 或 2。
    默认倍率值1适用于公文、表单、场景文字等单一文本框二十五至三十字情形。
    对于长文本框一行五十字排版场景，或用默认值识别一行三四十字印刷文字出现大量错误时，建议设置为2。
    调大该参数会增加识别过程的计算资源开销。
    ''')):
    return fx

@app.post('/ocr/classic', tags=[Tags.ocr], summary='图像文字识别',
        response_description='''
    检测及识别结果对象。

    - **boxes**: 检测框数组，元素为4x2二维数组，记录框的x1y1,x2y2,x3y3,x4y4值
    - **results**: 识别结果数组，元素是长度2的数组，表示识别文字及置信度
    ''')
def ocr_classic(img: torch.Tensor = Depends(device_tensor),
        text_det: Tuple[np.ndarray, np.ndarray] = Depends(text_det),
        model = Depends(get_ocr_model),
        fx: int = Depends(ocr_param_fx)):
    '''
    检测图像上的文本并识别文字

    '''
    boxes, rects = text_det
    if len(boxes) < 1:
        return { 'boxes': [], 'results': [] }
    with torch.inference_mode():
        rec_res = model.text_rec_classic(img, boxes, rects, fx); del img
    return { 'boxes': boxes.tolist(), 'results': rec_res }

@app.post('/ocr/catid', tags=[Tags.ocr], summary='图像文本鉴别',
        response_description='''
    鉴别结果对象。

    - **results**: 鉴别结果数组，元素是长度5的数组。完全匹配时前两个值有意义，
    后三个值为空值(null)；非完全匹配时后三个值供评估参考。各值含义如下：
    匹配到的鉴别候选项列表中的项 字符串
    文字相似度 完全匹配为1，否则0至1之间 数字
    文字识别原始解码结果 字符串
    匹配项损失值 对数尺度 数字
    原始解码结果损失值 对数尺度 数字
    ''')
def ocr_catid(img: torch.Tensor = Depends(device_tensor),
        cdanswers: list[str] = Query(description='鉴别候选项列表'),
        text_det: Tuple[np.ndarray, np.ndarray] = Depends(text_det),
        model = Depends(get_ocr_model),
        fx: int = Depends(ocr_param_fx)):
    '''
    本功能一般用于鉴别文档图像所属类别，当类别名称以文字标题等形式出现时，
    使用本功能一般能取得比较好的效果。
    检测图像上的文本框并与鉴别项列表中各文本项比对，找出合适的匹配项。
    当文字识别结果有误差时，该方法可从候选列表中找出在 OCR
    意义上预测损失相近的匹配结果。

    - cdanswers: 必填 鉴别候选项列表
    '''
    boxes, rects = text_det
    if len(boxes) < 1:
        return { 'boxes': [], 'results': [] }
    rec_res = model.text_catid(img, boxes, rects, cdanswers, fx); del img
    return { 'results': rec_res }

class CatalogInitializerIn(BaseModel):
    input_path: DirectoryPath
    model_name: str
    app_user_dir: pathlib.Path
    model_dir: pathlib.Path
    var_data_dir: pathlib.Path

class CatalogInitializer(CatalogInitializerIn):
    tool_layout: Any
    tool_ocr: Any
    w2v_path: FilePath
    stopped = False
    statobj : dict[str, Any] = dict(stage=0, stages=0, processed=0, total=0)

_catalog_train_dict = {}

def _catalog_train_task(x: CatalogInitializerIn, ocr
        , layout, conf: Settings):
    model_name = x.model_name
    try:
        u: CatalogInitializer = CatalogInitializer(**x.dict(),
            tool_ocr=ocr, tool_layout=layout, w2v_path=conf.nlp_res_w2v_path)
        _catalog_train_dict[model_name] = u.statobj
        from catalog_compile.extend_train.init_train import extract_rule
        extract_rule(u)
    except Exception as e:
        e = Exception(str(e), _catalog_train_dict.get(model_name))
        _catalog_train_dict[model_name] = e
        raise e

@app.post('/catalog_train', status_code=202)
def catalog_train(x: CatalogInitializerIn,
        response: Response, tasks: BackgroundTasks, ocr
        = Depends(get_ocr_model), layout
        = Depends(get_layout_model), conf : Settings
        = Depends(get_conf)):
    tasks.add_task(_catalog_train_task, x, ocr, layout, conf)
    model_name = x.model_name
    response.headers['Retry-After'] = '3'
    response.headers['Location'] = quote(f'/catalog_train/{model_name}/status')
    return x

@app.get('/catalog_train/{model_name}/status', status_code=202,
    response_description='正在运行',
    responses={
        201: { 'description': '创建成功' }, 404: { 'description': '没有任务' },
        200: { 'description': '执行出错' }
    })
def catalog_train_status(model_name: str,
        response: Response):
    status = 'proceeding'
    if model_name in _catalog_train_dict:
        e = _catalog_train_dict[model_name]
        if isinstance(e, Exception):
            response.status_code = 200
            del _catalog_train_dict[model_name]
            e = dict(zip(['cause', 'statobj'], e.args))
            status = 'error'
        elif e['stages'] and e['stage'] == e['stages']:
            response.status_code = 201
            del _catalog_train_dict[model_name]
            status = 'created'
        else:
            response.headers['Retry-After'] = '5'
            response.headers['Location'] = quote(f'/catalog_train/{model_name}/status')
    else:
        response.status_code = 404
        status = 'not found'
        e = { 'model_name': model_name, 'error': 'not found' }
    return { 'status': status, 'detail': e }

_catalog_opened = {}

class CatalogCompilerIn(BaseModel):
    model_name: str            = 'test'
    model_dir: pathlib.Path    = pathlib.Path('pics/cat1')
    app_user_dir: pathlib.Path = pathlib.Path('pics/cat1/user')

class CatalogCompiler(BaseModel):
    model_path: FilePath
    suffix_path: FilePath
    order_path: FilePath
    attach_path: FilePath
    w2v_path: FilePath

def catalog_compiler(x: CatalogCompilerIn, conf: Settings
        = Depends(get_conf)):
    try:
        return CatalogCompiler(
            model_path = x.model_dir / 'index' / x.model_name / 'index.nn',
            suffix_path = x.app_user_dir / x.model_name / 'suffix.dat',
            order_path = x.app_user_dir / x.model_name / 'order.txt',
            attach_path = x.app_user_dir / x.model_name / 'attach.txt',
            w2v_path = conf.nlp_res_w2v_path,
            )
    except ValidationError as e:
        raise RequestValidationError(e.raw_errors)

def catalog_create_suffix_map(suffix_path):
    with open(suffix_path, 'r', encoding='utf-8') as f:
        return dict([w.strip() for w in line.split('=-')] for line in f)

def catalog_create_order_map(order_path, attach_path):
    d = dict()
    with open(order_path, 'r', encoding='utf-8') as f:
        for line in f:
            v, k = [w.strip() for w in line.split('=-')]
            d[k] = (int(v), [])
    with open(attach_path, 'r', encoding='utf-8') as f:
        for line in f:
            k, v = [w.strip() for w in line.split('=-')]
            d[k][1].append(v)
    return d

@app.post('/catalog/{ident}')
async def catalog_open(ident: str, x: CatalogCompiler = Depends(catalog_compiler),
    ocr = Depends(get_ocr_model), layout = Depends(get_layout_model)):
    from catalog_compile.predict import get_w2v, Index
    from catalog_compile.ocr_process import single_image_layout_ocr
    index = Index(get_w2v(x.w2v_path), str(x.model_path))
    suffix_map = catalog_create_suffix_map(x.suffix_path)
    order_map = catalog_create_order_map(x.order_path, x.attach_path)
    _catalog_opened[ident] = (index, partial(single_image_layout_ocr,
        ocr=ocr, layout=layout, reserve_pred_numbers=10), suffix_map, order_map)
    return x

@app.delete('/catalog/{ident}')
async def catalog_close(ident: str):
    if ident in _catalog_opened:
        del _catalog_opened[ident]
    else:
        raise HTTPException(status_code=404, detail={ 'ident': ident,
            'error': 'not found' })
    return 'ok'

@app.post('/catalog/{ident}/predict')
def catalog_predict(ident: str, device_tensor: torch.Tensor
        = Depends(device_tensor)):
    if ident not in _catalog_opened:
        raise HTTPException(status_code=404, detail={ 'ident': ident,
            'error': 'not found' })
    index, ocr, suffix_map, order_map = _catalog_opened[ident]
    _, text, biaoti = ocr(device_tensor)
    intent, score = index.search(text, biaoti)
    if intent not in suffix_map:
        intent = '0'
    suffix = suffix_map[intent]
    assert score > 0
    return (intent, score, suffix)

class CatalogPredVal(NamedTuple):
    intent: str
    score: float
    suffix: str

class CatalogRenameIn(BaseModel):
    predicts: list[CatalogPredVal]

class CatalogRenameOut(BaseModel):
    early_stop: Optional[int]
    predicts: list[CatalogPredVal]

@app.post('/catalog/{ident}/rename')
def catalog_rename(ident: str, rename_in: CatalogRenameIn,
    contract_one: bool = False):
    if ident not in _catalog_opened:
        raise HTTPException(status_code=404, detail={ 'ident': ident,
            'error': 'not found' })
    index, ocr, suffix_map, order_map = _catalog_opened[ident]
    predicts = rename_in.predicts
    item  = (None, (1, [])) # (k, (order, attaches))
    early_stop = None
    # handle_classes_order
    for i in range(len(predicts)):
        intent, score, suffix = predicts[i]
        if intent == '0' or suffix in item[1][1]:
            if item[0]:
                suffix = item[0]
                predicts[i] = intent, score, suffix
            else:
                early_stop = i
                break
        elif suffix in order_map:
            new_item = suffix, order_map[suffix]
            if new_item[1][0] < item[1][0]:
                early_stop = i
                break
            else:
                item = new_item
        else:
            item = (None, (item[1][0], []))
    # handle_contract_one
    if contract_one:
        j, k, name = len(predicts), -1, ''
        for i in range(len(predicts)):
            intent, score, suffix = predicts[i]
            if '合同' in suffix:
                if i < j:
                    j, name = i, suffix
                else:
                    k = i
        for i in range(j, k+1):
            predicts[i] = predicts[i]._replace(suffix=name)
    return { 'early_stop': early_stop, 'predicts': predicts }


@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

@app.post('/admin/cuda_empty_cache')
async def _cuda_empty_cache():
    a = gc.collect()
    a1,_ = torch.cuda.mem_get_info()
    torch.cuda.empty_cache()
    a2,_ = torch.cuda.mem_get_info()
    return { 'reclaimed_objects': a, 'reclaimed_cuda_memory': a2-a1 }



@app.get('/docs', include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + ' - Swagger UI',
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        # swagger_js_url='https://unpkg.com/swagger-ui-dist@4/swagger-ui-bundle.js',
        # swagger_css_url='https://unpkg.com/swagger-ui-dist@4/swagger-ui.css',
    )


if __name__ == '__main__':
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=7999, log_config="static/config/log.json")