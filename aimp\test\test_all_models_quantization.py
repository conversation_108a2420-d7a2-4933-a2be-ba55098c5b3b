#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全模型量化测试脚本

测试系统中所有深度学习模型的量化效果，包括：
- OCR模型 (检测、识别、分类)
- 版面分析模型
- 污点检测模型
- 关键点检测模型
- 图像分类模型
- 对象检测模型

作者: xubiyun
日期: 2025-06-16
"""

import os
import sys
import time
import torch
import numpy as np
import cv2
from pathlib import Path
import json
from typing import Dict, List, Any
import argparse

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from model_quantization import ModelQuantizationManager, get_quantization_manager


class AllModelsQuantizationTester:
    """全模型量化测试器"""
    
    def __init__(self):
        self.dtm_path = Path("dtm")
        self.test_results = {}
        
        # 系统中所有模型的配置
        self.model_configs = {
            # OCR模型
            'det_model': {'path': 'dtm/001-1.pt', 'description': '文本检测模型'},
            'cls_model': {'path': 'dtm/003.pt', 'description': '文本分类模型'},
            'cls_vertical_model': {'path': 'dtm/006.pt', 'description': '垂直文本分类模型'},
            'rec_model': {'path': 'dtm/tp1t7.pt', 'description': '文本识别模型'},
            
            # 版面分析和检测模型
            'layout_model': {'path': 'dtm/la_dp09-1.pt', 'description': '版面分析模型'},
            'stain_model': {'path': 'dtm/dt36-1.pt', 'description': '污点检测模型'},
            'det0789_model': {'path': 'dtm/det0789.pt', 'description': '对象检测模型'},
            
            # 关键点和几何处理模型
            'kp_model': {'path': 'dtm/kp-180000-tf19-481.pt', 'description': '关键点检测模型'},
            'pe_model': {'path': 'dtm/mlim2a.pt', 'description': '透视变换模型'},
            
            # 图像分类模型
            'v69_model': {'path': 'dtm/192-v69.pt', 'description': '证件分类模型'}
        }
        
    def check_model_files(self) -> Dict[str, bool]:
        """检查模型文件是否存在"""
        print("🔍 检查模型文件...")
        
        file_status = {}
        for model_name, config in self.model_configs.items():
            model_path = Path(config['path'])
            exists = model_path.exists()
            file_status[model_name] = exists
            
            status_icon = "✅" if exists else "❌"
            print(f"  {status_icon} {config['description']}: {model_path}")
            
        return file_status
    
    def create_test_data(self) -> Dict[str, Any]:
        """创建测试数据"""
        print("🖼️  创建测试数据...")
        
        # 创建测试图片
        test_img = np.ones((400, 600, 3), dtype=np.uint8) * 255
        cv2.putText(test_img, "Model Quantization Test", (50, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(test_img, "All Models Performance", (50, 200), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(test_img, "CPU Optimization", (50, 300), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # 转换为tensor
        test_tensor = torch.as_tensor(test_img)[None,:].permute([0,3,1,2]).to(torch.float32)
        
        return {
            'image': test_img,
            'tensor': test_tensor,
            'resized_192': cv2.resize(test_img, (192, 192)),
            'resized_384': cv2.resize(test_img, (384, 384)),
            'resized_481': cv2.resize(test_img, (481, 481))
        }
    
    def test_model_loading(self, model_name: str, model_path: str, 
                          enable_quantization: bool = True, device: str = 'cpu') -> Dict[str, Any]:
        """测试单个模型的加载和量化"""
        print(f"  🧪 测试 {model_name} ({'量化' if enable_quantization else '原始'})...")
        
        result = {
            'model_name': model_name,
            'quantization_enabled': enable_quantization,
            'device': device,
            'success': False,
            'load_time': 0,
            'inference_time': 0,
            'error': None
        }
        
        try:
            # 获取量化管理器
            manager = get_quantization_manager(enable_quantization, device)
            
            # 测试模型加载时间
            start_time = time.time()
            model = manager.load_and_quantize(model_path, device)
            load_time = time.time() - start_time
            
            result['load_time'] = load_time
            result['success'] = True
            
            print(f"    ✅ 加载成功: {load_time:.3f}s")
            
            # 简单推理测试（如果可能）
            if self._can_test_inference(model_name):
                inference_time = self._test_model_inference(model, model_name)
                result['inference_time'] = inference_time
                print(f"    ⚡ 推理时间: {inference_time:.3f}s")
            
        except Exception as e:
            result['error'] = str(e)
            print(f"    ❌ 失败: {e}")
        
        return result
    
    def _can_test_inference(self, model_name: str) -> bool:
        """判断是否可以测试推理"""
        # 某些模型需要特殊的输入格式，暂时跳过推理测试
        skip_inference = ['kp_model', 'pe_model']
        return model_name not in skip_inference
    
    def _test_model_inference(self, model: torch.nn.Module, model_name: str) -> float:
        """测试模型推理时间"""
        test_data = self.create_test_data()
        
        # 根据模型类型选择合适的输入
        if model_name == 'v69_model':
            # 分类模型需要归一化输入
            input_tensor = torch.as_tensor(test_data['resized_192'])[None,:].permute([0,3,1,2]).to(torch.float32) / 255.0
        else:
            # 其他模型使用标准输入
            input_tensor = test_data['tensor']
        
        # 预热
        with torch.inference_mode():
            try:
                _ = model([input_tensor[0]])
            except:
                # 如果推理失败，返回0
                return 0
        
        # 实际测试
        start_time = time.time()
        with torch.inference_mode():
            for _ in range(3):  # 运行3次取平均
                _ = model([input_tensor[0]])
        
        inference_time = (time.time() - start_time) / 3
        return inference_time
    
    def run_comprehensive_test(self, device: str = 'cpu') -> Dict[str, Any]:
        """运行全面的量化测试"""
        print("🚀 开始全模型量化测试")
        print("=" * 60)
        
        # 检查模型文件
        file_status = self.check_model_files()
        
        # 创建测试数据
        test_data = self.create_test_data()
        
        # 测试结果
        results = {
            'device': device,
            'file_status': file_status,
            'model_results': {},
            'summary': {}
        }
        
        # 测试每个模型
        for model_name, config in self.model_configs.items():
            if not file_status.get(model_name, False):
                print(f"⏭️  跳过 {model_name} (文件不存在)")
                continue
            
            print(f"\n📋 测试 {config['description']} ({model_name})")
            
            model_results = {}
            
            # 测试原始模型
            original_result = self.test_model_loading(
                model_name, config['path'], enable_quantization=False, device=device
            )
            model_results['original'] = original_result
            
            # 测试量化模型
            quantized_result = self.test_model_loading(
                model_name, config['path'], enable_quantization=True, device=device
            )
            model_results['quantized'] = quantized_result
            
            # 计算性能对比
            if original_result['success'] and quantized_result['success']:
                load_speedup = original_result['load_time'] / quantized_result['load_time'] if quantized_result['load_time'] > 0 else 1
                
                comparison = {
                    'load_speedup': load_speedup,
                    'load_time_saved': original_result['load_time'] - quantized_result['load_time']
                }
                
                if original_result['inference_time'] > 0 and quantized_result['inference_time'] > 0:
                    inference_speedup = original_result['inference_time'] / quantized_result['inference_time']
                    comparison['inference_speedup'] = inference_speedup
                    comparison['inference_time_saved'] = original_result['inference_time'] - quantized_result['inference_time']
                
                model_results['comparison'] = comparison
                
                print(f"    📈 加载速度提升: {load_speedup:.2f}x")
                if 'inference_speedup' in comparison:
                    print(f"    ⚡ 推理速度提升: {comparison['inference_speedup']:.2f}x")
            
            results['model_results'][model_name] = model_results
        
        # 生成总结
        results['summary'] = self._generate_summary(results['model_results'])
        
        return results
    
    def _generate_summary(self, model_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成测试总结"""
        summary = {
            'total_models': len(model_results),
            'successful_quantizations': 0,
            'average_load_speedup': 0,
            'average_inference_speedup': 0,
            'total_load_time_saved': 0,
            'total_inference_time_saved': 0
        }
        
        load_speedups = []
        inference_speedups = []
        
        for model_name, results in model_results.items():
            if 'comparison' in results:
                summary['successful_quantizations'] += 1
                
                comparison = results['comparison']
                load_speedups.append(comparison['load_speedup'])
                summary['total_load_time_saved'] += comparison['load_time_saved']
                
                if 'inference_speedup' in comparison:
                    inference_speedups.append(comparison['inference_speedup'])
                    summary['total_inference_time_saved'] += comparison['inference_time_saved']
        
        if load_speedups:
            summary['average_load_speedup'] = np.mean(load_speedups)
        
        if inference_speedups:
            summary['average_inference_speedup'] = np.mean(inference_speedups)
        
        return summary
    
    def print_summary(self, results: Dict[str, Any]):
        """打印测试总结"""
        summary = results['summary']
        
        print("\n" + "=" * 60)
        print("📊 测试总结")
        print("=" * 60)
        
        print(f"总模型数量: {summary['total_models']}")
        print(f"成功量化: {summary['successful_quantizations']}")
        print(f"平均加载速度提升: {summary['average_load_speedup']:.2f}x")
        
        if summary['average_inference_speedup'] > 0:
            print(f"平均推理速度提升: {summary['average_inference_speedup']:.2f}x")
        
        print(f"总加载时间节省: {summary['total_load_time_saved']:.3f}s")
        
        if summary['total_inference_time_saved'] > 0:
            print(f"总推理时间节省: {summary['total_inference_time_saved']:.3f}s")
        
        # 量化策略信息
        manager = get_quantization_manager()
        quantization_info = manager.get_quantization_info()
        
        print(f"\n🔧 量化配置:")
        print(f"启用状态: {quantization_info['enabled']}")
        print(f"目标设备: {quantization_info['device']}")
        print(f"缓存模型: {quantization_info['cached_models']}")
    
    def save_results(self, results: Dict[str, Any], output_file: str = None):
        """保存测试结果"""
        if output_file is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            output_file = f"all_models_quantization_test_{timestamp}.json"
        
        output_path = Path(__file__).parent / output_file
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 详细结果已保存到: {output_path}")


def main():
    parser = argparse.ArgumentParser(description='全模型量化测试')
    parser.add_argument('--device', type=str, default='cpu', choices=['cpu', 'cuda'], 
                       help='测试设备')
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = AllModelsQuantizationTester()
    
    # 运行测试
    results = tester.run_comprehensive_test(args.device)
    
    # 打印总结
    tester.print_summary(results)
    
    # 保存结果
    tester.save_results(results)
    
    return results


if __name__ == "__main__":
    main()
