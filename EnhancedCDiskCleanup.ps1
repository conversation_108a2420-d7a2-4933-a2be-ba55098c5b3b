# Enhanced C Disk Cleanup Tool
# Author: Augment Agent
# Includes additional cleanup items

Write-Host "=== Enhanced C Disk Space Cleanup Tool ===" -ForegroundColor Cyan
Write-Host "Author: Augment Agent" -ForegroundColor Gray
Write-Host "=" * 50 -ForegroundColor Cyan

# Function to format file size
function Format-FileSize {
    param([long]$Size)
    if ($Size -gt 1GB) {
        return "{0:N2} GB" -f ($Size / 1GB)
    }
    elseif ($Size -gt 1MB) {
        return "{0:N2} MB" -f ($Size / 1MB)
    }
    else {
        return "{0:N2} KB" -f ($Size / 1KB)
    }
}

# Function to get folder size safely
function Get-SafeFolderSize {
    param([string]$Path)
    try {
        if (Test-Path $Path) {
            $size = (Get-ChildItem $Path -Recurse -File -ErrorAction SilentlyContinue | 
                    Measure-Object -Property Length -Sum).Sum
            if ($size -eq $null) { return 0 }
            return $size
        }
        return 0
    }
    catch {
        return 0
    }
}

# Check current disk space
Write-Host "`n=== Current C Disk Space ===" -ForegroundColor Cyan
try {
    $disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "C:"}
    $totalSize = $disk.Size
    $freeSpace = $disk.FreeSpace
    $usedSpace = $totalSize - $freeSpace
    $percentFree = ($freeSpace / $totalSize) * 100
    
    Write-Host "Total Size: $(Format-FileSize $totalSize)" -ForegroundColor White
    Write-Host "Used Space: $(Format-FileSize $usedSpace)" -ForegroundColor Yellow
    Write-Host "Free Space: $(Format-FileSize $freeSpace)" -ForegroundColor Green
    Write-Host "Free Percentage: $([math]::Round($percentFree,2))%" -ForegroundColor Green
}
catch {
    Write-Host "Error checking disk space" -ForegroundColor Red
}

# Check additional cleanup candidates
Write-Host "`n=== Additional Cleanup Candidates ===" -ForegroundColor Cyan

# Check Recycle Bin
try {
    $recycleBinSize = 0
    $shell = New-Object -ComObject Shell.Application
    $recycleBin = $shell.Namespace(10)
    if ($recycleBin) {
        foreach ($item in $recycleBin.Items()) {
            $recycleBinSize += $item.Size
        }
    }
    Write-Host "Recycle Bin: $(Format-FileSize $recycleBinSize)" -ForegroundColor White
}
catch {
    Write-Host "Recycle Bin: Unable to check" -ForegroundColor Gray
}

# Check browser caches
$userProfile = $env:USERPROFILE
$browserCaches = @{
    "Chrome Cache" = "$userProfile\AppData\Local\Google\Chrome\User Data\Default\Cache"
    "Edge Cache" = "$userProfile\AppData\Local\Microsoft\Edge\User Data\Default\Cache"
    "Firefox Cache" = "$userProfile\AppData\Local\Mozilla\Firefox\Profiles"
}

foreach ($browser in $browserCaches.Keys) {
    $path = $browserCaches[$browser]
    if (Test-Path $path) {
        $size = Get-SafeFolderSize $path
        if ($size -gt 10MB) {
            Write-Host "$browser : $(Format-FileSize $size)" -ForegroundColor White
        }
    }
}

# Check Windows.old
if (Test-Path "C:\Windows.old") {
    $windowsOldSize = Get-SafeFolderSize "C:\Windows.old"
    Write-Host "Windows.old: $(Format-FileSize $windowsOldSize)" -ForegroundColor White
}

# Check hibernation file
if (Test-Path "C:\hiberfil.sys") {
    $hibernationSize = (Get-Item "C:\hiberfil.sys" -Force).Length
    Write-Host "Hibernation file: $(Format-FileSize $hibernationSize)" -ForegroundColor White
}

# Ask user what to clean
Write-Host "`n=== Cleanup Options ===" -ForegroundColor Yellow
Write-Host "1. Basic cleanup (temp files, caches)" -ForegroundColor White
Write-Host "2. Enhanced cleanup (includes browsers, recycle bin)" -ForegroundColor White
Write-Host "3. Deep cleanup (includes system logs, hibernation)" -ForegroundColor White
Write-Host "4. Custom cleanup (choose items)" -ForegroundColor White
Write-Host "5. Just show results (no cleanup)" -ForegroundColor White
Write-Host "6. Exit" -ForegroundColor White

$choice = Read-Host "Please choose (1-6)"

$totalCleaned = 0

if ($choice -eq "1" -or $choice -eq "2" -or $choice -eq "3") {
    Write-Host "`n=== Starting Cleanup ===" -ForegroundColor Cyan
    
    # Basic cleanup - temp files
    Write-Host "Cleaning temp files..." -ForegroundColor Yellow
    $tempPaths = @($env:TEMP, "C:\Windows\Temp", "C:\ProgramData\temp")
    
    foreach ($tempPath in $tempPaths) {
        if (Test-Path $tempPath) {
            try {
                $beforeSize = Get-SafeFolderSize $tempPath
                Get-ChildItem $tempPath -Recurse -ErrorAction SilentlyContinue | 
                    Remove-Item -Force -Recurse -ErrorAction SilentlyContinue
                $afterSize = Get-SafeFolderSize $tempPath
                $cleaned = $beforeSize - $afterSize
                $totalCleaned += $cleaned
                Write-Host "Cleaned $tempPath : $(Format-FileSize $cleaned)" -ForegroundColor Green
            }
            catch {
                Write-Host "Error cleaning $tempPath" -ForegroundColor Red
            }
        }
    }
    
    # Basic cleanup - Package Cache
    Write-Host "Cleaning Package Cache..." -ForegroundColor Yellow
    $packageCachePath = "C:\ProgramData\Package Cache"
    if (Test-Path $packageCachePath) {
        try {
            $beforeSize = Get-SafeFolderSize $packageCachePath
            Remove-Item "$packageCachePath\*" -Recurse -Force -ErrorAction SilentlyContinue
            $afterSize = Get-SafeFolderSize $packageCachePath
            $cleaned = $beforeSize - $afterSize
            $totalCleaned += $cleaned
            Write-Host "Cleaned Package Cache : $(Format-FileSize $cleaned)" -ForegroundColor Green
        }
        catch {
            Write-Host "Error cleaning Package Cache" -ForegroundColor Red
        }
    }
    
    # Basic cleanup - Conda cache
    Write-Host "Checking Conda cache..." -ForegroundColor Yellow
    try {
        $condaCheck = conda --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Cleaning Conda cache..." -ForegroundColor Yellow
            conda clean --all --yes 2>$null
            Write-Host "Conda cache cleaned" -ForegroundColor Green
        }
        else {
            Write-Host "Conda not found, skipping" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "Conda not available" -ForegroundColor Gray
    }
    
    # Enhanced cleanup
    if ($choice -eq "2" -or $choice -eq "3") {
        # Clean browser caches
        Write-Host "Cleaning browser caches..." -ForegroundColor Yellow
        foreach ($browser in $browserCaches.Keys) {
            $path = $browserCaches[$browser]
            if (Test-Path $path) {
                try {
                    $beforeSize = Get-SafeFolderSize $path
                    Remove-Item "$path\*" -Recurse -Force -ErrorAction SilentlyContinue
                    $afterSize = Get-SafeFolderSize $path
                    $cleaned = $beforeSize - $afterSize
                    $totalCleaned += $cleaned
                    Write-Host "Cleaned $browser : $(Format-FileSize $cleaned)" -ForegroundColor Green
                }
                catch {
                    Write-Host "Error cleaning $browser" -ForegroundColor Red
                }
            }
        }
        
        # Clean Recycle Bin
        Write-Host "Emptying Recycle Bin..." -ForegroundColor Yellow
        try {
            Clear-RecycleBin -Force -ErrorAction SilentlyContinue
            Write-Host "Recycle Bin emptied" -ForegroundColor Green
        }
        catch {
            Write-Host "Error emptying Recycle Bin" -ForegroundColor Red
        }
    }
    
    # Deep cleanup
    if ($choice -eq "3") {
        # Clean system logs
        Write-Host "Cleaning system logs..." -ForegroundColor Yellow
        try {
            $logCount = 0
            wevtutil el | ForEach-Object { 
                wevtutil cl $_ 2>$null
                if ($LASTEXITCODE -eq 0) { $logCount++ }
            }
            Write-Host "Cleaned $logCount log files" -ForegroundColor Green
        }
        catch {
            Write-Host "Error cleaning system logs" -ForegroundColor Red
        }
        
        # Disk Cleanup utility
        Write-Host "Running Windows Disk Cleanup..." -ForegroundColor Yellow
        try {
            Start-Process cleanmgr -ArgumentList "/sagerun:1" -Wait -WindowStyle Hidden
            Write-Host "Windows Disk Cleanup completed" -ForegroundColor Green
        }
        catch {
            Write-Host "Error running Disk Cleanup" -ForegroundColor Red
        }
    }
    
    # Windows Update cache (all levels)
    Write-Host "Cleaning Windows Update cache..." -ForegroundColor Yellow
    $updateCachePath = "C:\Windows\SoftwareDistribution\Download"
    if (Test-Path $updateCachePath) {
        try {
            $beforeSize = Get-SafeFolderSize $updateCachePath
            Stop-Service wuauserv -Force -ErrorAction SilentlyContinue
            Remove-Item "$updateCachePath\*" -Recurse -Force -ErrorAction SilentlyContinue
            Start-Service wuauserv -ErrorAction SilentlyContinue
            $afterSize = Get-SafeFolderSize $updateCachePath
            $cleaned = $beforeSize - $afterSize
            $totalCleaned += $cleaned
            Write-Host "Cleaned Windows Update cache : $(Format-FileSize $cleaned)" -ForegroundColor Green
        }
        catch {
            Write-Host "Error cleaning Windows Update cache" -ForegroundColor Red
        }
    }
    
    # Show final results
    Write-Host "`n=== Cleanup Summary ===" -ForegroundColor Green
    Write-Host "Total space freed: $(Format-FileSize $totalCleaned)" -ForegroundColor Green
    
    # Check disk space again
    Write-Host "`n=== Final Disk Space ===" -ForegroundColor Cyan
    try {
        $disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "C:"}
        $newFreeSpace = $disk.FreeSpace
        $newPercentFree = ($newFreeSpace / $disk.Size) * 100
        
        Write-Host "Free Space: $(Format-FileSize $newFreeSpace)" -ForegroundColor Green
        Write-Host "Free Percentage: $([math]::Round($newPercentFree,2))%" -ForegroundColor Green
    }
    catch {
        Write-Host "Error checking final disk space" -ForegroundColor Red
    }
}
elseif ($choice -eq "4") {
    Write-Host "Custom cleanup options coming soon..." -ForegroundColor Yellow
}
elseif ($choice -eq "5") {
    Write-Host "Results shown above. No cleanup performed." -ForegroundColor Green
}
else {
    Write-Host "Exiting..." -ForegroundColor Green
}

Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = Read-Host
