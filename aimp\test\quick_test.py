#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速量化测试脚本

快速验证量化优化效果的简单脚本

作者: xubiyun  
日期: 2025-06-16
"""

import os
import sys
import time
import torch
import numpy as np
import cv2

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from myocr import OCRSystem


def create_test_image():
    """创建测试图片"""
    img = np.ones((400, 600, 3), dtype=np.uint8) * 255
    
    # 添加测试文字
    cv2.putText(img, "Quantization Test", (50, 100), 
               cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 2)
    cv2.putText(img, "Performance Benchmark", (50, 180), 
               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(img, "CPU Optimization", (50, 260), 
               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    return img


def test_ocr_performance(ocr_system, test_img, system_name, num_runs=5):
    """测试OCR系统性能"""
    print(f"\n🧪 测试 {system_name}...")
    
    times = []
    results = []
    
    for i in range(num_runs):
        # 转换图片格式
        img_tensor = torch.as_tensor(test_img, device=ocr_system.device)[None,:].permute([0,3,1,2]).to(torch.float32)
        
        # 计时开始
        start_time = time.time()
        
        with torch.inference_mode():
            # 文本检测
            boxes, rects = ocr_system.text_detect(img_tensor)
            
            # 文本识别
            if len(boxes) > 0:
                rec_results = ocr_system.text_rec_classic(img_tensor, boxes, rects)
            else:
                rec_results = []
        
        # 计时结束
        end_time = time.time()
        elapsed = end_time - start_time
        times.append(elapsed)
        
        if i == 0:  # 只保存第一次的结果用于对比
            results = rec_results
        
        print(f"  运行 {i+1}: {elapsed:.3f}s")
    
    avg_time = np.mean(times)
    std_time = np.std(times)
    
    print(f"  📊 平均时间: {avg_time:.3f}s ± {std_time:.3f}s")
    print(f"  📝 检测文本框: {len(boxes)}")
    print(f"  🔤 识别文本: {[r[0] for r in results]}")
    
    return {
        'avg_time': avg_time,
        'std_time': std_time,
        'times': times,
        'num_boxes': len(boxes),
        'texts': [r[0] for r in results]
    }


def main():
    print("🚀 OCR性能对比测试")
    print("=" * 50)

    # 检查可用设备
    devices = ['cpu']
    if torch.cuda.is_available():
        devices.append('cuda')
        print("🎮 检测到CUDA GPU，将进行CPU vs GPU对比测试")
    else:
        print("💻 仅检测到CPU，将进行CPU量化对比测试")

    # 创建测试图片
    print("🖼️  创建测试图片...")
    test_img = create_test_image()

    # 保存测试图片以便查看
    cv2.imwrite("test/test_image.jpg", test_img)
    print("   测试图片已保存为: test/test_image.jpg")

    # 模型配置
    base_model_config = {
        'det_model_path': 'dtm/001-1.pt',
        'cls_model_path': 'dtm/003.pt',
        'cls_vertical_model_path': 'dtm/006.pt',
        'rec_model_path': 'dtm/tp1t7.pt',
        'rec_char_dict_path': 'dtm/ppocr_keys_v1.txt'
    }

    all_results = {}

    try:
        # 测试不同配置
        test_configs = []

        # CPU测试配置
        test_configs.append({
            'name': 'CPU原始模型',
            'device': 'cpu',
            'quantization': False,
            'config': {**base_model_config, 'device': 'cpu'}
        })

        test_configs.append({
            'name': 'CPU量化模型',
            'device': 'cpu',
            'quantization': True,
            'config': {**base_model_config, 'device': 'cpu'}
        })

        # GPU测试配置（如果可用）
        if 'cuda' in devices:
            test_configs.append({
                'name': 'GPU原始模型',
                'device': 'cuda',
                'quantization': False,  # GPU上不使用量化
                'config': {**base_model_config, 'device': 'cuda'}
            })

        # 运行所有测试
        for i, test_config in enumerate(test_configs, 1):
            print(f"\n{i}️⃣ 创建{test_config['name']}...")

            ocr_system = OCRSystem(
                enable_quantization=test_config['quantization'],
                **test_config['config']
            )

            results = test_ocr_performance(
                ocr_system, test_img, test_config['name'], num_runs=3
            )

            all_results[test_config['name']] = results
        
        # 性能对比分析
        print("\n📊 性能对比结果:")
        print("=" * 50)

        # 显示所有测试结果
        for name, result in all_results.items():
            fps = 1.0 / result['avg_time']
            print(f"{name:12s}: {result['avg_time']:.3f}s/张 ({fps:.2f} FPS)")

        # 详细对比分析
        if len(all_results) >= 2:

            # CPU对比：原始 vs 量化
            if 'CPU原始模型' in all_results and 'CPU量化模型' in all_results:
                cpu_orig = all_results['CPU原始模型']
                cpu_quant = all_results['CPU量化模型']

                speedup = cpu_orig['avg_time'] / cpu_quant['avg_time']
                time_saved = cpu_orig['avg_time'] - cpu_quant['avg_time']

                print(f"\n🔥 CPU量化优化效果:")
                print(f"   速度提升: {speedup:.2f}x")
                print(f"   时间节省: {time_saved:.3f}s ({time_saved/cpu_orig['avg_time']*100:.1f}%)")

                # 检查精度
                orig_texts = set(cpu_orig['texts'])
                quant_texts = set(cpu_quant['texts'])

                if orig_texts == quant_texts:
                    print("   ✅ 识别结果完全一致，精度无损失")
                else:
                    print("   ⚠️ 识别结果有差异")

            # GPU vs CPU对比
            if 'GPU原始模型' in all_results and 'CPU原始模型' in all_results:
                gpu_result = all_results['GPU原始模型']
                cpu_result = all_results['CPU原始模型']

                speedup = cpu_result['avg_time'] / gpu_result['avg_time']

                print(f"\n🎮 GPU vs CPU对比:")
                print(f"   GPU加速倍数: {speedup:.2f}x")
                print(f"   GPU时间: {gpu_result['avg_time']:.3f}s")
                print(f"   CPU时间: {cpu_result['avg_time']:.3f}s")

            # GPU vs CPU量化对比
            if 'GPU原始模型' in all_results and 'CPU量化模型' in all_results:
                gpu_result = all_results['GPU原始模型']
                cpu_quant_result = all_results['CPU量化模型']

                speedup = cpu_quant_result['avg_time'] / gpu_result['avg_time']

                print(f"\n⚡ GPU vs CPU量化对比:")
                print(f"   GPU相对CPU量化的优势: {speedup:.2f}x")
                if speedup < 1:
                    print("   🎉 CPU量化性能超越GPU!")
                else:
                    print("   📈 GPU仍有性能优势")

        # 目标达成评估
        target_fps = 1.0
        print(f"\n🎯 性能目标评估 (目标: {target_fps:.1f} FPS):")

        for name, result in all_results.items():
            fps = 1.0 / result['avg_time']
            if fps >= target_fps:
                print(f"   ✅ {name}: {fps:.2f} FPS - 已达标!")
            else:
                needed_speedup = target_fps / fps
                print(f"   ❌ {name}: {fps:.2f} FPS - 需要{needed_speedup:.2f}x优化")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ 测试完成!")


if __name__ == "__main__":
    main()
